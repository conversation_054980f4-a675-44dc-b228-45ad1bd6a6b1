import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const PrivacyPolicyScreen = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#bb1010" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Policy</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <Text style={styles.lastUpdated}>Last updated: July 18, 2025</Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>1. Information We Collect</Text>
            <Text style={styles.paragraph}>
              For a better experience while using our Service, we may require
              you to provide us with certain personally identifiable
              information, including but not limited to your internet and Wi-Fi
              connection status. The information that we request is retained on
              your device and is not collected by us in any way. The app does
              use third party services that may collect information used to
              identify you.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>2. Log Data</Text>
            <Text style={styles.paragraph}>
              We want to inform you that whenever you use our Service, in case
              of an error in the app we collect data and information (through
              third party products) on your phone called Log Data. This Log Data
              may include information such as your device's Internet Protocol
              (“IP”) address, device name, operating system version,
              configuration of the app when utilizing our Service, the time and
              date of your use of the Service, and other statistics.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>3. Service Providers</Text>
            <Text style={styles.paragraph}>
              We may employ third-party companies and individuals due to the
              following reasons: To facilitate our Service; To provide the
              Service on our behalf; To perform Service-related services; or To
              assist us in analyzing how our Service is used. We want to inform
              users of this Service that these third parties have access to your
              Personal Information. The reason is to perform the tasks assigned
              to them on our behalf. However, they are obligated not to disclose
              or use the information for any other purpose.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>4. Data Security</Text>
            <Text style={styles.paragraph}>
              We value your trust in providing us your Personal Information,
              thus we are striving to use commercially acceptable means of
              protecting it. But remember that no method of transmission over
              the internet, or method of electronic storage is 100% secure and
              reliable, and we cannot guarantee its absolute security
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>5. Links to Other Sites</Text>
            <Text style={styles.paragraph}>
              This Service may contain links to other sites. If you click on a
              third-party link, you will be directed to that site. Note that
              these external sites are not operated by us. Therefore, we
              strongly advise you to review the Privacy Policy of these
              websites. We have no control over, and assume no responsibility
              for the content, privacy policies, or practices of any third-party
              sites or services.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>6. Cookies</Text>
            <Text style={styles.paragraph}>
              Cookies are files with small amount of data that is commonly used
              as an anonymous unique identifier. These are sent to your browser
              from the website that you visit and are stored on your device's
              internal memory. This Services does not uses these “cookies”
              explicitly. However, the app may use third party code and
              libraries that use “cookies” to collection information and to
              improve their services. You have the option to either accept or
              refuse these cookies, and know when a cookie is being sent to your
              device. If you choose to refuse our cookies, you may not be able
              to use some portions of this Service.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>7. Children’s Privacy</Text>
            <Text style={styles.paragraph}>
              This Services do not address anyone under the age of 13. We do not
              knowingly collect personal identifiable information from children
              under 13. In the case we discover that a child under 13 has
              provided us with personal information, we immediately delete this
              from our servers. If you are a parent or guardian and you are
              aware that your child has provided us with personal information,
              please contact us so that we will be able to do necessary actions.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              8. Changes to This Privacy Policy
            </Text>
            <Text style={styles.paragraph}>
              We may update our Privacy Policy from time to time. Thus, you are
              advised to review this page periodically for any changes. We will
              notify you of any changes by posting the new Privacy Policy on
              this page. These changes are effective immediately, after they are
              posted on this page
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>9. Contact Us</Text>
            <Text style={styles.paragraph}>
              If you have any questions or suggestions about our Privacy Policy,
              do not hesitate to contact us. Our email address is
              <EMAIL>
            </Text>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              By using our services, you acknowledge that you have read and
              understood this privacy policy.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.3,
    fontWeight: "bold",
    color: "#333",
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  content: {
    backgroundColor: "#fff",
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lastUpdated: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#666",
    fontStyle: "italic",
    marginBottom: 24,
    textAlign: "center",
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  paragraph: {
    fontSize: BASE_FONT_SIZE,
    color: "#444",
    lineHeight: 22,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: BASE_FONT_SIZE,
    color: "#444",
    lineHeight: 20,
    marginBottom: 4,
    marginLeft: 8,
  },
  footer: {
    backgroundColor: "#f8f9fa",
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  footerText: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#666",
    textAlign: "center",
    fontStyle: "italic",
    lineHeight: 18,
  },
});

export default PrivacyPolicyScreen;
