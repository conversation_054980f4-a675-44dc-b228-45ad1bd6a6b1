import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, Linking, Dimensions, useWindowDimensions, Animated, ActivityIndicator, Alert, Share } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import EnquiryModal from '../components/EnquiryModal';
import { getVehicleById } from '../utils/api';
import ImageCarousel from '../components/ImageCarousel';

const TruckDetailsScreen = ({ route, navigation }) => {
  const { width } = useWindowDimensions();
  const { vehicle } = route.params || {};
  const [truck, setTruck] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEnquiryModalVisible, setIsEnquiryModalVisible] = useState(false);

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];

  useEffect(() => {
    const fetchTruckDetails = async () => {
      try {
        setLoading(true);

        // Check if vehicle data exists and has required ID
        if (!vehicle || !vehicle.vehicle_id) {
          console.error('No vehicle data or vehicle_id provided to TruckDetailsScreen');
          setError('Vehicle information is missing. Please try again.');
          setLoading(false);
          return;
        }

        console.log('Fetching vehicle details for ID:', vehicle.vehicle_id);
        const response = await getVehicleById(vehicle.vehicle_id);
        setTruck(response.vehicle);
        setError(null);
      } catch (err) {
        console.error('Error fetching truck details:', err);
        setError('Failed to load truck details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (vehicle?.vehicle_id) {
      fetchTruckDetails();
    } else {
      console.error('No vehicle or vehicle_id provided to TruckDetailsScreen');
      setError('Vehicle information is missing. Please try again.');
      setLoading(false);
    }
  }, [vehicle?.vehicle_id]);

  useEffect(() => {
    if (!loading && !error) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [loading, error]);

  const handleShare = async () => {
    if (!truck) return;
    
    try {
      const shareUrl = `https://trucks24.co.za/vehicle.php?id=${truck.vehicle_id}`;
      const price = truck.price ? `R${truck.price.toLocaleString()}` : 'Contact for price';

      const result = await Share.share({
        message: `Check out this ${truck.year} ${truck.make} ${truck.model} for ${price} on Trucks On Sale!\n${shareUrl}`,
        url: shareUrl,
        title: `${truck.year} ${truck.make} ${truck.model} - Trucks On Sale`
      });
      
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
          console.log('Shared with activity type:', result.activityType);
        } else {
          // shared
          console.log('Shared successfully');
        }
      }
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert(
        'Sharing Error',
        'Unable to share this listing. Please try again.',
        [
          {
            text: 'OK',
            onPress: () => console.log('OK Pressed')
          }
        ]
      );
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#bb1010" />
        <Text style={styles.loadingText}>Loading truck details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#bb1010" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            setError(null);
            fetchTruckDetails();
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!truck) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#bb1010" />
        <Text style={styles.errorText}>Truck not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={{ width: '100%', alignItems: 'center', justifyContent: 'center' }}>
        <ImageCarousel
          images={truck.images?.length ? truck.images : (vehicle.images || [])}
          width={width}
          height={width > 768 ? 400 : 300}
          showIndicators={true}
          showCounter={true}
          borderRadius={0}
          style={{}}
        />
      </View>
      <Animated.View style={[styles.content, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>{`${truck.year} ${truck.make}`}</Text>
            <Text style={styles.model}>{truck.model}</Text>
          </View>
          <TouchableOpacity
            style={styles.shareButton}
            onPress={handleShare}
          >
            <Ionicons name="share-social-outline" size={24} color="#fff" />
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.price}>R{truck.price.toLocaleString()}</Text>

        {/* Basic Information */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Year" value={truck.year || 'N/A'} />
            <SpecItem title="Condition" value={truck.condition_type || truck.condition || 'N/A'} />
            <SpecItem title="Mileage" value={truck.mileage ? `${truck.mileage.toLocaleString()} km` : 'N/A'} />
            <SpecItem title="Color" value={truck.color || 'N/A'} />
            <SpecItem title="Region" value={truck.region || 'N/A'} />
            <SpecItem title="City" value={truck.city || 'N/A'} />
          </View>
        </View>

        {/* Engine & Performance */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Engine & Performance</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Engine Type" value={truck.engine_type || 'N/A'} />
            <SpecItem title="Engine Capacity" value={truck.engine_capacity || 'N/A'} />
            <SpecItem title="Horsepower" value={truck.horsepower ? `${truck.horsepower} HP` : 'N/A'} />
            <SpecItem title="Fuel Type" value={truck.fuel_type || 'N/A'} />
            <SpecItem title="Transmission" value={truck.transmission || 'N/A'} />
            {truck.hours_used > 0 ? (
              <SpecItem title="Hours Used" value={`${truck.hours_used.toLocaleString()} hrs`} />
            ) : null}
          </View>
        </View>

        {/* Vehicle Details */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Vehicle Details</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Category" value={truck.category || 'N/A'} />
            {truck.subcategory ? (
              <SpecItem title="Subcategory" value={truck.subcategory} />
            ) : null}
            {truck.variant ? (
              <SpecItem title="Variant" value={truck.variant} />
            ) : null}
            <SpecItem title="Condition Rating" value={truck.condition_rating || 'N/A'} />
            {truck.vin_number ? (
              <SpecItem title="VIN Number" value={truck.vin_number} />
            ) : null}
            {truck.registration_number ? (
              <SpecItem title="Registration" value={truck.registration_number} />
            ) : null}
          </View>
        </View>

        {/* Features & Condition */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Features & Condition</Text>
          <View style={styles.featuresList}>
            {truck.no_accidents ? (
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>No Accidents</Text>
              </View>
            ) : null}
            {truck.warranty ? (
              <View style={styles.featureItem}>
                <Ionicons name="shield-checkmark" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Warranty Available</Text>
              </View>
            ) : null}
            {truck.service_history ? (
              <View style={styles.featureItem}>
                <Ionicons name="document-text" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Service History Available</Text>
              </View>
            ) : null}
            {truck.roadworthy ? (
              <View style={styles.featureItem}>
                <Ionicons name="car-sport" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Roadworthy Certificate</Text>
              </View>
            ) : null}
            {truck.finance_available ? (
              <View style={styles.featureItem}>
                <Ionicons name="card" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Finance Available</Text>
              </View>
            ) : null}
            {truck.trade_in ? (
              <View style={styles.featureItem}>
                <Ionicons name="swap-horizontal" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Trade-in Accepted</Text>
              </View>
            ) : null}
          </View>
        </View>

        {/* Description */}
        {truck.description ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.descriptionText}>{truck.description}</Text>
          </View>
        ) : null}

        {/* Additional Features */}
        {truck.features ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Additional Features</Text>
            <Text style={styles.descriptionText}>{truck.features}</Text>
          </View>
        ) : null}

        {/* Warranty Details */}
        {truck.warranty_details ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Warranty Details</Text>
            <Text style={styles.descriptionText}>{truck.warranty_details}</Text>
          </View>
        ) : null}

        <TouchableOpacity
          style={styles.enquiryButton}
          onPress={() => setIsEnquiryModalVisible(true)}
        >
          <Ionicons name="mail-outline" size={24} color="#fff" />
          <Text style={styles.enquiryButtonText}>Make Enquiry</Text>
        </TouchableOpacity>
      </Animated.View>

      <EnquiryModal
        visible={isEnquiryModalVisible}
        onClose={() => setIsEnquiryModalVisible(false)}
        truckDetails={truck}
      />
    </ScrollView>
  );
};

const SpecItem = ({ title, value }) => (
  <View style={styles.specItem}>
    <Text style={styles.specTitle}>{title}</Text>
    <Text style={styles.specValue}>{value}</Text>
  </View>
);

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 14 : 16;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#333',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 24,
    backgroundColor: '#bb1010',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH > 768 ? 400 : 300,
    aspectRatio: 16 / 9,
  },
  content: {
    padding: 16, // Match HireDetailsScreen
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16, // Match HireDetailsScreen
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: 'bold',
    color: '#333',
  },
  model: {
    fontSize: BASE_FONT_SIZE * 1.125,
    color: '#bb1010',
    marginTop: 4,
  },
  shareButton: {
    backgroundColor: '#bb1010',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  shareButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  price: {
    fontSize: BASE_FONT_SIZE * 2,
    fontWeight: 'bold',
    color: '#bb1010', // Updated red color
    marginBottom: 24, // Match HireDetailsScreen pattern
  },
  specificationContainer: {
    backgroundColor: '#f5f5f5', // Match HireDetailsScreen
    borderRadius: 12,
    padding: 16, // Match HireDetailsScreen
    marginBottom: 24, // Match HireDetailsScreen
    borderWidth: 1,
    borderColor: '#e0e0e0', // Match HireDetailsScreen
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.125, // Match HireDetailsScreen
    fontWeight: 'bold',
    color: '#333', // Match HireDetailsScreen
    marginBottom: 16, // Match HireDetailsScreen
  },
  specificationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16, // Match HireDetailsScreen
  },
  specItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8, // Match HireDetailsScreen
    padding: 12, // Match HireDetailsScreen
    width: SCREEN_WIDTH > 768 ? '31%' : '47%', // Match HireDetailsScreen
    borderWidth: 1,
    borderColor: '#e0e0e0', // Match HireDetailsScreen
  },
  specTitle: {
    color: '#bb1010', // Updated red color
    fontSize: BASE_FONT_SIZE * 0.875, // Match HireDetailsScreen
    marginBottom: 4, // Match HireDetailsScreen
  },
  specValue: {
    color: '#333', // Match HireDetailsScreen
    fontSize: BASE_FONT_SIZE, // Match HireDetailsScreen
    fontWeight: '600', // Match HireDetailsScreen
  },
  enquiryButton: {
    backgroundColor: '#bb1010', // Preserve red color
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16, // Match HireDetailsScreen pattern
    borderRadius: 8, // Match HireDetailsScreen pattern
    marginTop: 24, // Match HireDetailsScreen pattern
    gap: 8, // Match HireDetailsScreen pattern
  },
  enquiryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
  },
  featuresList: {
    marginTop: 12, // Match HireDetailsScreen
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8, // Match HireDetailsScreen
    paddingVertical: 4, // Match HireDetailsScreen
  },
  featureText: {
    marginLeft: 8, // Match HireDetailsScreen
    fontSize: BASE_FONT_SIZE, // Match HireDetailsScreen
    color: '#333', // Match HireDetailsScreen
    flex: 1,
  },
  descriptionText: {
    fontSize: BASE_FONT_SIZE, // Match HireDetailsScreen
    color: '#666', // Match HireDetailsScreen
    lineHeight: BASE_FONT_SIZE * 1.5, // Match HireDetailsScreen
    marginTop: 8, // Match HireDetailsScreen
  },
});

export default TruckDetailsScreen;