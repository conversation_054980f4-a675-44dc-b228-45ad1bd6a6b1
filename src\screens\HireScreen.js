import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
  Entypo,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import TruckCard from "../components/TruckCard";
import { getLatestVehicles, getHireVehicles } from "../utils/api";
import { 
  shuffleArray, 
  setupAutoRefresh, 
  formatRefreshTime, 
  logRefreshActivity,
  REFRESH_INTERVALS 
} from "../utils/listingUtils";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const HireScreen = () => {
  const navigation = useNavigation();
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [lastRefreshTime, setLastRefreshTime] = useState(Date.now());

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      console.log("HireScreen: Fetching hire vehicles...");

      let hireVehicles = [];

      // Try multiple approaches to get hire vehicles
      try {
        // First try: Use dedicated hire vehicles API
        console.log("HireScreen: Trying dedicated hire API...");
        const hireResponse = await getHireVehicles(50, 'hire_trucks');
        console.log("HireScreen: Dedicated hire API response:", hireResponse);

        if (hireResponse.vehicles && hireResponse.vehicles.length > 0) {
          hireVehicles = hireResponse.vehicles;
          console.log("HireScreen: Found vehicles via dedicated API:", hireVehicles.length);
        }
      } catch (hireError) {
        console.log("HireScreen: Dedicated hire API failed:", hireError.message);
      }

      // Fallback: Get all vehicles and filter for hire
      if (hireVehicles.length === 0) {
        console.log("HireScreen: Trying fallback - getting all vehicles...");
        try {
          const allVehiclesResponse = await getLatestVehicles(100);
          console.log("HireScreen: All vehicles response:", allVehiclesResponse);

          const allVehicles = allVehiclesResponse.vehicles || [];
          console.log("HireScreen: Total vehicles fetched:", allVehicles.length);

          // Filter for hire vehicles using strict criteria
          hireVehicles = allVehicles.filter(vehicle => {
            // Only include vehicles that are explicitly marked for hire
            const isHire = vehicle.listing_type === 'hire' ||
                          vehicle.for_hire === true ||
                          vehicle.for_hire === 1 ||
                          (vehicle.category && vehicle.category.toLowerCase().includes('hire'));

            // Don't include vehicles just because they have rental rates
            // as regular sale vehicles might also have rental rates

            if (isHire) {
              console.log("HireScreen: Found hire vehicle:", {
                id: vehicle.vehicle_id,
                make: vehicle.make,
                model: vehicle.model,
                listing_type: vehicle.listing_type,
                for_hire: vehicle.for_hire,
                category: vehicle.category
              });
            }

            return isHire;
          });

          console.log("HireScreen: Filtered hire vehicles:", hireVehicles.length);
        } catch (fallbackError) {
          console.error("HireScreen: Fallback API also failed:", fallbackError);
          throw fallbackError;
        }
      }

      // Don't create fake hire vehicles - if no hire vehicles exist, show empty state
      if (hireVehicles.length === 0) {
        console.log("HireScreen: No hire vehicles found in database");
      }

      const shuffledVehicles = shuffleArray(hireVehicles);

      console.log("HireScreen: Final hire vehicles count:", shuffledVehicles.length);
      if (shuffledVehicles.length > 0) {
        console.log("HireScreen: Sample hire vehicle:", {
          id: shuffledVehicles[0].vehicle_id,
          make: shuffledVehicles[0].make,
          model: shuffledVehicles[0].model,
          listing_type: shuffledVehicles[0].listing_type,
          daily_rate: shuffledVehicles[0].daily_rate
        });
      }

      setVehicles(shuffledVehicles);
      setLastRefreshTime(Date.now());
      setError(shuffledVehicles.length === 0 ? "No hire vehicles available at the moment." : null);

      logRefreshActivity("HireScreen", shuffledVehicles.length);
    } catch (err) {
      console.error("Error fetching hire vehicles:", err);
      setError("Failed to load hire vehicles. Please try again later.");
      setVehicles([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
    
    const refreshInterval = setupAutoRefresh(fetchVehicles, REFRESH_INTERVALS.HOME);
    return () => clearInterval(refreshInterval);
  }, []);

  const filterVehicles = (vehicles) => {
    return vehicles.filter((item) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const searchableText = `${item.make} ${item.model} ${item.year} ${item.description || ""}`.toLowerCase();
        if (!searchableText.includes(query)) return false;
      }
      return true;
    });
  };

  const filteredVehicles = filterVehicles(vehicles);

  const handleTruckPress = (item) => {
    navigation.navigate("HireDetails", { vehicle: item });
  };

  const renderVehicleItem = ({ item }) => (
    <View style={styles.cardWrapper}>
      <TruckCard
        item={item}
        onPress={() => handleTruckPress(item)}
        width={SCREEN_WIDTH * 0.42} // Reduced from 0.45
        style={styles.card}
      />
      <View style={styles.hireBadge}>
        <Text style={styles.hireText}>For Hire</Text>
      </View>
    </View>
  );

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Vehicle Hire</Text>
            <Text style={styles.headerSubtitle}>
              {loading ? "Loading..." : `${filteredVehicles.length} vehicles available for hire`}
            </Text>
            <Text style={styles.refreshIndicator}>
              Updated {formatRefreshTime(lastRefreshTime)}
            </Text>
          </View>
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={fetchVehicles}
          >
            <Ionicons name="refresh" size={20} color="#FF0000" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchBar}>
          <Ionicons
            name="search"
            size={20}
            color="#666"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Search vehicles for hire..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* Info Banner */}
      <View style={styles.infoBanner}>
        <Ionicons name="time" size={20} color="#FF8C00" />
        <Text style={styles.infoText}>
          Rent vehicles by the day, week, or month. Perfect for short-term projects.
        </Text>
      </View>

      {/* Main Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF0000" />
          <Text style={styles.loadingText}>Loading hire vehicles...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchVehicles}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      ) : filteredVehicles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={48} color="#999" />
          <Text style={styles.emptyTitle}>No hire vehicles found</Text>
          <Text style={styles.emptySubtitle}>
            {vehicles.length > 0
              ? `${vehicles.length} hire vehicles loaded but none match your search`
              : "No vehicles are currently available for hire. Check back later for new hire opportunities."
            }
          </Text>
          {__DEV__ && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugText}>Debug Info:</Text>
              <Text style={styles.debugText}>Total vehicles: {vehicles.length}</Text>
              <Text style={styles.debugText}>Filtered vehicles: {filteredVehicles.length}</Text>
              <Text style={styles.debugText}>Search query: "{searchQuery}"</Text>
              <TouchableOpacity
                style={styles.debugButton}
                onPress={() => {
                  console.log("HireScreen Debug - All vehicles:", vehicles);
                  console.log("HireScreen Debug - Filtered vehicles:", filteredVehicles);
                  alert(`Debug: ${vehicles.length} total, ${filteredVehicles.length} filtered`);
                }}
              >
                <Text style={styles.debugButtonText}>Log Debug Info</Text>
              </TouchableOpacity>
            </View>
          )}
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchVehicles}
          >
            <Text style={styles.retryButtonText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredVehicles}
          renderItem={renderVehicleItem}
          keyExtractor={(item) => `hire-${item.vehicle_id || item.id}`}
          numColumns={2}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  headerContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: Platform.OS === "ios" ? 50 : 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: "bold",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 2,
  },
  refreshIndicator: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#f5f5f5",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  infoBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF4E6",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#FFE0B3",
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#FF8C00",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    color: "#666",
    fontSize: BASE_FONT_SIZE,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  errorText: {
    fontSize: BASE_FONT_SIZE,
    color: "#FF6B6B",
    textAlign: "center",
    marginTop: 12,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#FF0000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#333",
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    textAlign: "center",
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  row: {
    justifyContent: "space-between",
    paddingHorizontal: 4,
  },
  cardWrapper: {
    width: "48%",
    marginBottom: 16,
    position: "relative",
  },
  card: {
    width: "100%",
  },
  hireBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#FF8C00",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  hireText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE * 0.8,
    fontWeight: "bold",
  },
  debugContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  debugText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
    marginBottom: 5,
  },
  debugButton: {
    marginTop: 10,
    padding: 10,
    backgroundColor: "#007AFF",
    borderRadius: 6,
    alignItems: "center",
  },
  debugButtonText: {
    color: "white",
    fontSize: BASE_FONT_SIZE * 0.9,
    fontWeight: "600",
  },
});

export default HireScreen;
