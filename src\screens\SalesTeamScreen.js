import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Modal,
  ScrollView,
  Dimensions,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

// Sample sales team data
const SALES_TEAM = [
  {
    id: '1',
    name: '<PERSON>',
    position: 'Sales Manager',
    age: 42,
    experience: '15 years',
    specialization: 'Heavy-duty Trucks',
    email: '<EMAIL>',
    phone: '+27 71 234 5678',
    bio: '<PERSON> has been in the truck sales industry for over 15 years. He specializes in heavy-duty trucks and has extensive knowledge of the trucking industry in South Africa.',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
    rating: 4.9,
    salesCompleted: 342,
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'Sales Consultant',
    age: 35,
    experience: '10 years',
    specialization: 'Construction Equipment',
    email: '<EMAIL>',
    phone: '+27 82 345 6789',
    bio: '<PERSON> is our construction equipment expert with 10 years of experience. She has helped numerous clients find the perfect equipment for their construction needs.',
    image: 'https://randomuser.me/api/portraits/women/2.jpg',
    rating: 4.8,
    salesCompleted: 215,
  },
  {
    id: '3',
    name: 'Michael Ndlovu',
    position: 'Senior Sales Consultant',
    age: 38,
    experience: '12 years',
    specialization: 'Trailers & Transport',
    email: '<EMAIL>',
    phone: '+27 83 456 7890',
    bio: 'Michael is our trailer specialist with over 12 years in the transport industry. His knowledge of logistics and transportation requirements makes him an invaluable asset to our clients.',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    rating: 4.7,
    salesCompleted: 278,
  },
  {
    id: '4',
    name: 'Thandi Nkosi',
    position: 'Sales Consultant',
    age: 29,
    experience: '5 years',
    specialization: 'Light Commercial Vehicles',
    email: '<EMAIL>',
    phone: '+27 84 567 8901',
    bio: 'Thandi specializes in light commercial vehicles and has been with our team for 5 years. Her attention to detail and customer service has earned her a loyal client base.',
    image: 'https://randomuser.me/api/portraits/women/4.jpg',
    rating: 4.6,
    salesCompleted: 127,
  },
  {
    id: '5',
    name: 'David van der Merwe',
    position: 'Fleet Sales Specialist',
    age: 45,
    experience: '18 years',
    specialization: 'Fleet Solutions',
    email: '<EMAIL>',
    phone: '+27 85 678 9012',
    bio: 'David has 18 years of experience in fleet management and sales. He provides comprehensive solutions for businesses looking to establish or upgrade their vehicle fleets.',
    image: 'https://randomuser.me/api/portraits/men/5.jpg',
    rating: 4.9,
    salesCompleted: 412,
  },
  {
    id: '6',
    name: 'Nomsa Dlamini',
    position: 'Finance Specialist',
    age: 33,
    experience: '8 years',
    specialization: 'Financing & Leasing',
    email: '<EMAIL>',
    phone: '+27 86 789 0123',
    bio: 'Nomsa is our financing expert with 8 years in vehicle financing. She helps clients navigate the complexities of truck financing, leasing options, and payment plans.',
    image: 'https://randomuser.me/api/portraits/women/6.jpg',
    rating: 4.8,
    salesCompleted: 198,
  },
];

const SalesTeamScreen = () => {
  const [selectedMember, setSelectedMember] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  const handleMemberPress = (member) => {
    setSelectedMember(member);
    setModalVisible(true);
  };

  const handleCall = (phone) => {
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = (email) => {
    Linking.openURL(`mailto:${email}`);
  };

  const renderMemberItem = ({ item }) => (
    <TouchableOpacity
      style={styles.memberCard}
      onPress={() => handleMemberPress(item)}
    >
      <Image source={{ uri: item.image }} style={styles.memberImage} />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{item.name}</Text>
        <Text style={styles.memberPosition}>{item.position}</Text>
        <Text style={styles.memberSpecialization}>
          <Ionicons name="star" size={14} color="#FFD700" /> {item.specialization}
        </Text>
      </View>
      <View style={styles.arrowContainer}>
        <Ionicons name="chevron-forward" size={24} color="#666" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Our Sales Team</Text>
        <Text style={styles.headerSubtitle}>
          Meet our experienced professionals ready to help you find the perfect vehicle
        </Text>
      </View>

      <FlatList
        data={SALES_TEAM}
        renderItem={renderMemberItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
      />

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        {selectedMember && (
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>

              <ScrollView>
                <Image
                  source={{ uri: selectedMember.image }}
                  style={styles.modalImage}
                />

                <View style={styles.modalHeader}>
                  <Text style={styles.modalName}>{selectedMember.name}</Text>
                  <Text style={styles.modalPosition}>{selectedMember.position}</Text>
                </View>

                <View style={styles.statsContainer}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{selectedMember.experience}</Text>
                    <Text style={styles.statLabel}>Experience</Text>
                  </View>
                  <View style={styles.statDivider} />
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{selectedMember.rating}</Text>
                    <Text style={styles.statLabel}>Rating</Text>
                  </View>
                  <View style={styles.statDivider} />
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{selectedMember.salesCompleted}</Text>
                    <Text style={styles.statLabel}>Sales</Text>
                  </View>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>About</Text>
                  <Text style={styles.bioText}>{selectedMember.bio}</Text>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>Details</Text>
                  
                  <View style={styles.detailItem}>
                    <Ionicons name="person" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Age:</Text>
                    <Text style={styles.detailValue}>{selectedMember.age} years</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Ionicons name="briefcase" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Specialization:</Text>
                    <Text style={styles.detailValue}>{selectedMember.specialization}</Text>
                  </View>
                </View>

                <View style={styles.contactSection}>
                  <Text style={styles.sectionTitle}>Contact Information</Text>
                  
                  <TouchableOpacity
                    style={styles.contactButton}
                    onPress={() => handleCall(selectedMember.phone)}
                  >
                    <Ionicons name="call" size={20} color="#fff" />
                    <Text style={styles.contactButtonText}>Call</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.contactButton, styles.emailButton]}
                    onPress={() => handleEmail(selectedMember.email)}
                  >
                    <Ionicons name="mail" size={20} color="#fff" />
                    <Text style={styles.contactButtonText}>Email</Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>
            </View>
          </View>
        )}
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    backgroundColor: '#1a1a1a',
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.6,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: '#ccc',
  },
  listContainer: {
    padding: 16,
  },
  memberCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  memberImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  memberInfo: {
    flex: 1,
    marginLeft: 16,
  },
  memberName: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  memberPosition: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    marginBottom: 4,
  },
  memberSpecialization: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: '#888',
  },
  arrowContainer: {
    padding: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 15,
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalImage: {
    width: '100%',
    height: 200,
  },
  modalHeader: {
    padding: 16,
    backgroundColor: '#1a1a1a',
  },
  modalName: {
    fontSize: BASE_FONT_SIZE * 1.4,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  modalPosition: {
    fontSize: BASE_FONT_SIZE,
    color: '#ccc',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: '#666',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#eee',
    marginHorizontal: 8,
  },
  detailSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  bioText: {
    fontSize: BASE_FONT_SIZE,
    color: '#666',
    lineHeight: BASE_FONT_SIZE * 1.5,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
    width: 100,
  },
  detailValue: {
    fontSize: BASE_FONT_SIZE,
    color: '#666',
    flex: 1,
  },
  contactSection: {
    padding: 16,
  },
  contactButton: {
    flexDirection: 'row',
    backgroundColor: '#FF0000',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  emailButton: {
    backgroundColor: '#1a1a1a',
  },
  contactButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default SalesTeamScreen;