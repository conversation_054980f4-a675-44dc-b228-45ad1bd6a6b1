import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';


const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const SellTruckScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    title: '',
    make: '',
    model: '',
    year: '',
    price: '',
    description: '',
    category: 'Trucks', // Default category
    region: '',
    condition: 'Used', // Default condition
    fuelType: '',
    driveType: '',
    contactName: '',
    contactPhone: '',
    contactEmail: '',
  });
  
  const [images, setImages] = useState([]);
  const [activeCategory, setActiveCategory] = useState('Trucks');
  const [activeCondition, setActiveCondition] = useState('Used');
  
  const categories = ['Trucks', 'Construction', 'Trailers', 'Others'];
  const conditions = ['New', 'Used'];
  const regions = ['KwaZulu-Natal', 'Western Cape', 'Eastern Cape', 'Limpopo', 'Mpumalanga', 'Free State', 'Gauteng', 'North West', 'Northern Cape'];
  
  const handleInputChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };
  
  const handleCategorySelect = (category) => {
    setActiveCategory(category);
    handleInputChange('category', category);
  };
  
  const handleConditionSelect = (condition) => {
    setActiveCondition(condition);
    handleInputChange('condition', condition);
  };
  
  const pickImage = () => {

      Alert.alert('Maximum Images', 'You can upload a maximum of 5 images.');
    

   
    
   
  };
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Sell Your Vehicle</Text>
          <Text style={styles.headerSubtitle}>
            Fill in the details below to list your vehicle for sale
          </Text>
        </View>
        
        {/* Vehicle Category */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vehicle Category</Text>
          <View style={styles.categoryContainer}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  activeCategory === category && styles.activeCategoryButton,
                ]}
                onPress={() => handleCategorySelect(category)}
              >
                <Text
                  style={[
                    styles.categoryButtonText,
                    activeCategory === category && styles.activeCategoryButtonText,
                  ]}
                >
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Vehicle Condition */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vehicle Condition</Text>
          <View style={styles.conditionContainer}>
            {conditions.map((condition) => (
              <TouchableOpacity
                key={condition}
                style={[
                  styles.conditionButton,
                  activeCondition === condition && styles.activeConditionButton,
                ]}
                onPress={() => handleConditionSelect(condition)}
              >
                <Text
                  style={[
                    styles.conditionButtonText,
                    activeCondition === condition && styles.activeConditionButtonText,
                  ]}
                >
                  {condition}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Vehicle Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vehicle Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Listing Title *</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. 2020 Volvo FH16 750 Globetrotter"
              value={formData.title}
              onChangeText={(text) => handleInputChange('title', text)}
            />
          </View>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={styles.inputLabel}>Make *</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. Volvo"
                value={formData.make}
                onChangeText={(text) => handleInputChange('make', text)}
              />
            </View>
            
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={styles.inputLabel}>Model *</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. FH16 750"
                value={formData.model}
                onChangeText={(text) => handleInputChange('model', text)}
              />
            </View>
          </View>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={styles.inputLabel}>Year *</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. 2020"
                keyboardType="number-pad"
                value={formData.year}
                onChangeText={(text) => handleInputChange('year', text)}
              />
            </View>
            
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={styles.inputLabel}>Price (R) *</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. 500000"
                keyboardType="number-pad"
                value={formData.price}
                onChangeText={(text) => handleInputChange('price', text)}
              />
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Region *</Text>
            <View style={styles.pickerContainer}>
              <TextInput
                style={styles.input}
                placeholder="Select a region"
                value={formData.region}
                onFocus={() => {
                  // Show region picker or dropdown
                  // For simplicity, we'll just show an alert with options
                  Alert.alert(
                    'Select Region',
                    'Choose your region',
                    regions.map(region => ({
                      text: region,
                      onPress: () => handleInputChange('region', region)
                    }))
                  );
                }}
              />
              <Ionicons name="chevron-down" size={20} color="#666" style={styles.pickerIcon} />
            </View>
          </View>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={styles.inputLabel}>Fuel Type</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. Diesel"
                value={formData.fuelType}
                onChangeText={(text) => handleInputChange('fuelType', text)}
              />
            </View>
            
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={styles.inputLabel}>Drive Type</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. 6x4"
                value={formData.driveType}
                onChangeText={(text) => handleInputChange('driveType', text)}
              />
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Describe your vehicle, including its features, history, and condition"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              value={formData.description}
              onChangeText={(text) => handleInputChange('description', text)}
            />
          </View>
        </View>
        
        {/* Vehicle Images */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vehicle Images</Text>
          <Text style={styles.sectionSubtitle}>
            Upload up to 5 high-quality images of your vehicle (required)
          </Text>
          
          <View style={styles.imagesContainer}>
            {images.map((uri, index) => (
              <View key={index} style={styles.imageWrapper}>
                <Image source={{ uri }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => removeImage(index)}
                >
                  <Ionicons name="close-circle" size={24} color="#FF0000" />
                </TouchableOpacity>
              </View>
            ))}
            
            {images.length < 5 && (
              <TouchableOpacity style={styles.addImageButton} onPress={pickImage}>
                <Ionicons name="camera" size={32} color="#666" />
                <Text style={styles.addImageText}>Add Image</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Contact Name *</Text>
            <TextInput
              style={styles.input}
              placeholder="Your full name"
              value={formData.contactName}
              onChangeText={(text) => handleInputChange('contactName', text)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Phone Number *</Text>
            <TextInput
              style={styles.input}
              placeholder="Your phone number"
              keyboardType="phone-pad"
              value={formData.contactPhone}
              onChangeText={(text) => handleInputChange('contactPhone', text)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Email Address *</Text>
            <TextInput
              style={styles.input}
              placeholder="Your email address"
              keyboardType="email-address"
              autoCapitalize="none"
              value={formData.contactEmail}
              onChangeText={(text) => handleInputChange('contactEmail', text)}
            />
          </View>
        </View>
        
        {/* Submit Button */}
        <TouchableOpacity style={styles.submitButton} >
          <Text style={styles.submitButtonText}>Submit Listing</Text>
        </TouchableOpacity>
        
        <View style={styles.disclaimer}>
          <Text style={styles.disclaimerText}>
            By submitting this listing, you agree to our Terms of Service and Privacy Policy.
            Your listing will be reviewed before being published.
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    backgroundColor: '#1a1a1a',
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.6,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: '#ccc',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  sectionSubtitle: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    marginBottom: 16,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ddd',
    margin: 4,
  },
  activeCategoryButton: {
    backgroundColor: '#FF0000',
    borderColor: '#FF0000',
  },
  categoryButtonText: {
    fontSize: BASE_FONT_SIZE,
    color: '#333',
  },
  activeCategoryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  conditionContainer: {
    flexDirection: 'row',
  },
  conditionButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  conditionButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeConditionButton: {
    backgroundColor: '#bb1010',
    borderColor: '#bb1010',
  },
  conditionButtonText: {
    fontSize: BASE_FONT_SIZE,
    color: '#333',
  },
  activeConditionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    marginBottom: 6,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: BASE_FONT_SIZE,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
  },
  pickerContainer: {
    position: 'relative',
  },
  pickerIcon: {
    position: 'absolute',
    right: 12,
    top: 12,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  imageWrapper: {
    width: '33.33%',
    padding: 4,
    position: 'relative',
  },
  image: {
    width: '100%',
    aspectRatio: 16 / 9,
    borderRadius: 4,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
  addImageButton: {
    width: '33.33%',
    padding: 4,
  },
  addImageButton: {
    width: '33.33%',
    aspectRatio: 16 / 9,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
  },
  addImageText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: '#666',
    marginTop: 4,
  },
  submitButton: {
    backgroundColor: '#bb1010',
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 8,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: 'bold',
  },
  disclaimer: {
    marginHorizontal: 16,
    marginBottom: 32,
    marginTop: 8,
  },
  disclaimerText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: '#999',
    textAlign: 'center',
  },
});

export default SellTruckScreen;