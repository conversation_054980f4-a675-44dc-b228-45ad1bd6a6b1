import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, Dimensions, Animated, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import EnquiryModal from '../components/EnquiryModal';
import { getVehicleById } from '../utils/api';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 14 : 16;

const AdvancedSearchDetailsScreen = ({ route, navigation }) => {
  const { vehicle } = route.params;
  const [truck, setTruck] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEnquiryModalVisible, setIsEnquiryModalVisible] = useState(false);

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];

  useEffect(() => {
    const fetchTruckDetails = async () => {
      try {
        setLoading(true);
        const response = await getVehicleById(vehicle.vehicle_id);
        setTruck(response.vehicle);
        setError(null);
      } catch (err) {
        console.error('Error fetching truck details:', err);
        setError('Failed to load truck details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTruckDetails();
  }, [vehicle.vehicle_id]);

  useEffect(() => {
    if (!loading && !error) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [loading, error]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF0000" />
        <Text style={styles.loadingText}>Loading truck details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF0000" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!truck) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF0000" />
        <Text style={styles.errorText}>Truck not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Animated.Image
       source={{
        uri: truck.images?.[0]?.image_path
          || vehicle.images?.[0]?.image_path
          || 'https://via.placeholder.com/400x300?text=No+Image'
      }}
        style={[styles.image, { opacity: fadeAnim }]}
        resizeMode="cover"
      />
      <Animated.View style={[styles.content, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
        <View style={styles.header}>
          <Text style={styles.title}>{`${truck.year} ${truck.make}`}</Text>
          <Text style={styles.model}>{truck.model}</Text>
        </View>

        <Text style={styles.price}>R{truck.price.toLocaleString()}</Text>

        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Specifications</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Fuel Type" value={truck.fuel_type || 'N/A'} />
            <SpecItem title="Drive Type" value={truck.drive_type || 'N/A'} />
            <SpecItem title="Mileage" value={`${truck.mileage?.toLocaleString() || 'N/A'} km`} />
            <SpecItem title="Engine Type" value={truck.engine_type || 'N/A'} />
            <SpecItem title="Transmission" value={truck.transmission || 'N/A'} />
            <SpecItem title="Condition" value={truck.condition || 'N/A'} />
          </View>
        </View>

        <TouchableOpacity
          style={styles.enquiryButton}
          onPress={() => setIsEnquiryModalVisible(true)}
        >
          <Ionicons name="mail-outline" size={24} color="#fff" />
          <Text style={styles.enquiryButtonText}>Make Enquiry</Text>
        </TouchableOpacity>
      </Animated.View>

      <EnquiryModal
        visible={isEnquiryModalVisible}
        onClose={() => setIsEnquiryModalVisible(false)}
        truckDetails={truck}
      />
    </ScrollView>
  );
};

const SpecItem = ({ title, value }) => (
  <View style={styles.specItem}>
    <Text style={styles.specTitle}>{title}</Text>
    <Text style={styles.specValue}>{value}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#333',
    textAlign: 'center',
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH > 768 ? 400 : 300,
    aspectRatio: 16 / 9,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: 'bold',
    color: '#333',
  },
  model: {
    fontSize: BASE_FONT_SIZE * 1.125,
    color: '#bb1010',
    marginTop: 4,
  },
  price: {
    fontSize: BASE_FONT_SIZE * 2,
    fontWeight: 'bold',
    color: '#bb1010',
    marginBottom: 24,
  },
  specificationContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.125,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  specificationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  specItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    width: SCREEN_WIDTH > 768 ? '31%' : '47%',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  specTitle: {
    color: '#bb1010',
    fontSize: BASE_FONT_SIZE * 0.875,
    marginBottom: 4,
  },
  specValue: {
    color: '#333',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
  enquiryButton: {
    backgroundColor: '#bb1010',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 24,
    gap: 8,
  },
  enquiryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
  },
});

export default AdvancedSearchDetailsScreen;