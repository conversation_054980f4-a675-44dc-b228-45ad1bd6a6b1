import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  ScrollView,
  Platform,
} from "react-native";
import {
  MaterialCommunityIcons,
} from "@expo/vector-icons";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const AuctionScreen = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Subtle pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.02,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);



  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Simplified Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Vehicle Auctions</Text>
          <Text style={styles.headerSubtitle}>Live Bidding Platform</Text>
        </View>
      </View>

      {/* Simple Coming Soon Banner */}
      <Animated.View
        style={[
          styles.bannerContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <View style={styles.bannerContent}>
          {/* Icon */}
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons name="gavel" size={48} color="#8B5CF6" />
          </View>

          {/* Content */}
          <Text style={styles.bannerTitle}>Coming Soon!</Text>
          <Text style={styles.bannerSubtitle}>Vehicle Auction Platform</Text>
          <Text style={styles.bannerDescription}>
            We're building an exciting auction platform for you.
            Stay tuned for live bidding opportunities!
          </Text>
        </View>
      </Animated.View>

    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },

  // Header Styles
  headerContainer: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 24,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#e2e8f0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 2.2,
    fontWeight: "800",
    color: "#1e293b",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#64748b",
    fontWeight: "500",
    letterSpacing: 0.2,
  },

  // Simple Banner Styles
  bannerContainer: {
    marginHorizontal: 16,
    marginVertical: 32,
    backgroundColor: "#F3E5F5",
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8B5CF6",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bannerContent: {
    padding: 24,
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: 16,
  },
  bannerTitle: {
    fontSize: BASE_FONT_SIZE * 1.8,
    fontWeight: "bold",
    color: "#8B5CF6",
    marginBottom: 8,
    textAlign: "center",
  },
  bannerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: "600",
    color: "#7C3AED",
    marginBottom: 12,
    textAlign: "center",
  },
  bannerDescription: {
    fontSize: BASE_FONT_SIZE * 1.0,
    color: "#6B46C1",
    textAlign: "center",
    lineHeight: 20,
    paddingHorizontal: 8,
  },
});

export default AuctionScreen;
