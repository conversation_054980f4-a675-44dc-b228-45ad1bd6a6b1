import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ChatWithSales = () => {
  // WhatsApp number for sales team
  const SALES_WHATSAPP = '+27123456789'; // Replace with actual sales team number

  const handleWhatsAppPress = async () => {
    try {
      const message = 'Hello, I would like to inquire about your vehicles.';
      
      // Try web version first as it's more reliable
      const webUrl = `https://wa.me/${SALES_WHATSAPP}?text=${encodeURIComponent(message)}`;
      await Linking.openURL(webUrl);
      
    } catch (error) {
      console.error('WhatsApp sharing error:', error);
      Alert.alert(
        'Error',
        'Unable to open WhatsApp. Please make sure you have WhatsApp installed.',
        [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
      );
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handleWhatsAppPress}
      activeOpacity={0.8}
    >
      <View style={styles.iconContainer}>
        <Ionicons name="logo-whatsapp" size={24} color="#fff" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>Chat with Sales</Text>
        <Text style={styles.subtitle}>Get instant support via WhatsApp</Text>
      </View>
      <Ionicons name="chevron-forward" size={24} color="#666" />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#25D366', // WhatsApp green
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
});

export default ChatWithSales;