import React, { Component } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

class SafeScreenWrapper extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorCount: 0
    };
    this.mounted = true;
  }

  static getDerivedStateFromError(error) {
    console.error('SafeScreenWrapper: Error caught:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('SafeScreenWrapper: Component error:', error, errorInfo);
    this.setState(prevState => ({ 
      errorCount: prevState.errorCount + 1 
    }));
  }

  componentWillUnmount() {
    this.mounted = false;
  }

  handleRetry = () => {
    if (this.mounted) {
      this.setState({ 
        hasError: false, 
        error: null 
      });
    }
  };

  handleGoBack = () => {
    try {
      if (this.props.navigation && this.mounted) {
        this.props.navigation.goBack();
      }
    } catch (error) {
      console.error('SafeScreenWrapper: Navigation error:', error);
      // Force navigation to home as fallback
      try {
        if (this.props.navigation) {
          this.props.navigation.reset({
            index: 0,
            routes: [{ name: 'Home' }],
          });
        }
      } catch (resetError) {
        console.error('SafeScreenWrapper: Reset navigation error:', resetError);
      }
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
          <Text style={styles.errorMessage}>
            The screen encountered an error and needs to be reloaded.
          </Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.retryButton]}
              onPress={this.handleRetry}
            >
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.backButton]}
              onPress={this.handleGoBack}
            >
              <Text style={styles.buttonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
          
          {this.state.errorCount > 2 && (
            <Text style={styles.persistentErrorText}>
              If this error persists, please restart the app.
            </Text>
          )}
        </View>
      );
    }

    try {
      return this.props.children;
    } catch (renderError) {
      console.error('SafeScreenWrapper: Render error:', renderError);
      this.setState({ hasError: true, error: renderError });
      return null;
    }
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 15,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  retryButton: {
    backgroundColor: '#bb1010',
  },
  backButton: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  persistentErrorText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginTop: 20,
    fontStyle: 'italic',
  },
});

export default SafeScreenWrapper;
