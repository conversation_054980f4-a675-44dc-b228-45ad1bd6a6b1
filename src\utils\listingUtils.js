/**
 * Utility functions for managing vehicle listings
 */

/**
 * Shuffles an array randomly using <PERSON><PERSON><PERSON> algorithm
 * @param {Array} array - The array to shuffle
 * @returns {Array} - A new shuffled array
 */
export const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Sets up an auto-refresh interval for listings
 * @param {Function} refreshFunction - Function to call for refreshing
 * @param {number} intervalMinutes - Interval in minutes (default: 10)
 * @returns {number} - Interval ID for cleanup
 */
export const setupAutoRefresh = (refreshFunction, intervalMinutes = 10) => {
  const intervalMs = intervalMinutes * 60 * 1000;
  
  console.log(`Setting up auto-refresh every ${intervalMinutes} minutes for better listing visibility`);
  
  return setInterval(() => {
    console.log("Auto-refreshing listings for better visibility...");
    refreshFunction();
  }, intervalMs);
};

/**
 * Formats the last refresh time for display
 * @param {number} timestamp - Timestamp of last refresh
 * @returns {string} - Formatted time string
 */
export const formatRefreshTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString([], {
    hour: '2-digit', 
    minute: '2-digit'
  });
};

/**
 * Gets a random subset of vehicles for featured display
 * @param {Array} vehicles - Array of vehicles
 * @param {number} count - Number of vehicles to return
 * @returns {Array} - Random subset of vehicles
 */
export const getRandomVehicles = (vehicles, count = 15) => {
  const shuffled = shuffleArray(vehicles);
  return shuffled.slice(0, count);
};

/**
 * Rotates featured vehicles to ensure all get visibility
 * @param {Array} allVehicles - All available vehicles
 * @param {Array} currentFeatured - Currently featured vehicles
 * @param {number} rotationPercentage - Percentage to rotate (0-100)
 * @returns {Array} - New featured vehicles array
 */
export const rotateFeaturedVehicles = (allVehicles, currentFeatured, rotationPercentage = 30) => {
  if (!allVehicles.length || !currentFeatured.length) {
    return shuffleArray(allVehicles);
  }

  const rotationCount = Math.ceil((currentFeatured.length * rotationPercentage) / 100);
  const remainingCount = currentFeatured.length - rotationCount;
  
  // Keep some of the current featured vehicles
  const shuffledCurrent = shuffleArray(currentFeatured);
  const keptVehicles = shuffledCurrent.slice(0, remainingCount);
  
  // Get new vehicles that aren't currently featured
  const currentIds = new Set(currentFeatured.map(v => v.vehicle_id || v.id));
  const availableNew = allVehicles.filter(v => !currentIds.has(v.vehicle_id || v.id));
  const newVehicles = getRandomVehicles(availableNew, rotationCount);
  
  // Combine and shuffle the final result
  return shuffleArray([...keptVehicles, ...newVehicles]);
};

/**
 * Configuration for auto-refresh intervals
 */
export const REFRESH_INTERVALS = {
  FEATURED: 10, // 10 minutes
  LATEST: 10,   // 10 minutes
  SEARCH: 15,   // 15 minutes
  HOME: 10      // 10 minutes
};

/**
 * Logs refresh activity for debugging
 * @param {string} screenName - Name of the screen being refreshed
 * @param {number} vehicleCount - Number of vehicles refreshed
 */
export const logRefreshActivity = (screenName, vehicleCount) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${screenName}: Refreshed ${vehicleCount} vehicles for better visibility`);
};
