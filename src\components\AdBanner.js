import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const AdBanner = ({ 
  bannerType = 'full-width', // 'full-width' or 'box'
  bannerData = [], 
  rotationInterval = 6000,
  onBannerPress = null,
  style = {}
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const intervalRef = useRef(null);
  const isMountedRef = useRef(true);

  // Auto-rotation effect
  useEffect(() => {
    if (bannerData.length <= 1) return;

    const startRotation = () => {
      intervalRef.current = setInterval(() => {
        if (!isMountedRef.current) return;
        
        // Fade out
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          if (!isMountedRef.current) return;
          
          // Change banner
          setCurrentIndex((prevIndex) => 
            (prevIndex + 1) % bannerData.length
          );
          
          // Fade in
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }).start();
        });
      }, rotationInterval);
    };

    startRotation();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [bannerData.length, rotationInterval, fadeAnim]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Handle banner press
  const handleBannerPress = () => {
    if (!isMountedRef.current || !bannerData[currentIndex]) return;
    
    const banner = bannerData[currentIndex];
    
    if (onBannerPress) {
      onBannerPress(banner);
    } else {
      // Default action - show banner info
      Alert.alert(
        'Advertisement',
        banner.title || 'Advertisement Banner',
        [
          { text: 'Close', style: 'cancel' },
          { 
            text: 'Learn More', 
            onPress: () => {
              console.log('Banner clicked:', banner);
              // Future: Open URL or navigate to advertiser page
            }
          }
        ]
      );
    }
  };

  // Handle close banner
  const handleCloseBanner = () => {
    setIsVisible(false);
  };

  // Don't render if no data or not visible
  if (!bannerData.length || !isVisible || !bannerData[currentIndex]) {
    return null;
  }

  const currentBanner = bannerData[currentIndex];
  const isFullWidth = bannerType === 'full-width';

  return (
    <View style={[
      styles.container,
      isFullWidth ? styles.fullWidthContainer : styles.boxContainer,
      style
    ]}>
      <TouchableOpacity
        style={[
          styles.bannerTouchable,
          isFullWidth ? styles.fullWidthBanner : styles.boxBanner
        ]}
        onPress={handleBannerPress}
        activeOpacity={0.8}
      >
        <Animated.View style={[
          styles.bannerContent,
          { opacity: fadeAnim }
        ]}>
          {/* Background */}
          <View
            style={[
              styles.backgroundSolid,
              { backgroundColor: currentBanner.backgroundColor || '#bb1010' }
            ]}
          />

          {/* Banner Image (if provided) */}
          {currentBanner.imageUrl && (
            <Image
              source={{ uri: currentBanner.imageUrl }}
              style={[
                styles.bannerImage,
                isFullWidth ? styles.fullWidthImage : styles.boxImage
              ]}
              resizeMode="cover"
            />
          )}

          {/* Content Overlay */}
          <View style={[
            styles.contentOverlay,
            isFullWidth ? styles.fullWidthOverlay : styles.boxOverlay
          ]}>
            {/* Main Content */}
            <View style={styles.mainContent}>
              {/* Title */}
              <Text style={[
                styles.bannerTitle,
                isFullWidth ? styles.fullWidthTitle : styles.boxTitle
              ]} numberOfLines={isFullWidth ? 1 : 2}>
                {currentBanner.title}
              </Text>

              {/* Subtitle */}
              {currentBanner.subtitle && (
                <Text style={[
                  styles.bannerSubtitle,
                  isFullWidth ? styles.fullWidthSubtitle : styles.boxSubtitle
                ]} numberOfLines={isFullWidth ? 1 : 2}>
                  {currentBanner.subtitle}
                </Text>
              )}

              {/* Call to Action */}
              {currentBanner.ctaText && !isFullWidth && (
                <View style={styles.ctaButton}>
                  <Text style={styles.ctaText}>{currentBanner.ctaText}</Text>
                  <Ionicons name="arrow-forward" size={14} color="#fff" />
                </View>
              )}
            </View>

            {/* Banner Icon/Logo */}
            {currentBanner.icon && (
              <View style={styles.iconContainer}>
                <Ionicons 
                  name={currentBanner.icon} 
                  size={isFullWidth ? 24 : 32} 
                  color="#fff" 
                />
              </View>
            )}
          </View>

          {/* Rotation Indicator */}
          {bannerData.length > 1 && (
            <View style={[
              styles.indicatorContainer,
              isFullWidth ? styles.fullWidthIndicator : styles.boxIndicator
            ]}>
              {bannerData.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.indicator,
                    index === currentIndex ? styles.activeIndicator : styles.inactiveIndicator
                  ]}
                />
              ))}
            </View>
          )}
        </Animated.View>
      </TouchableOpacity>

      {/* Close Button */}
      <TouchableOpacity
        style={styles.closeButton}
        onPress={handleCloseBanner}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons name="close" size={16} color="#666" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    marginVertical: 8,
  },
  fullWidthContainer: {
    width: SCREEN_WIDTH -30,
    height: 90,
    marginHorizontal: 0,
  },
  boxContainer: {
    width: SCREEN_WIDTH - 32,
    height: 200,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  bannerTouchable: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fullWidthBanner: {
    borderRadius: 0,
  },
  boxBanner: {
    borderRadius: 12,
  },
  bannerContent: {
    flex: 1,
    position: 'relative',
  },
  backgroundSolid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bannerImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  fullWidthImage: {
    height: 90,
  },
  boxImage: {
    height: 200,
  },
  contentOverlay: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  fullWidthOverlay: {
    paddingVertical: 8,
  },
  boxOverlay: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  mainContent: {
    flex: 1,
  },
  bannerTitle: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  fullWidthTitle: {
    fontSize: 16,
  },
  boxTitle: {
    fontSize: 20,
    marginBottom: 8,
  },
  bannerSubtitle: {
    color: '#fff',
    opacity: 0.9,
  },
  fullWidthSubtitle: {
    fontSize: 12,
  },
  boxSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  ctaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  ctaText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicatorContainer: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullWidthIndicator: {
    bottom: 8,
    right: 16,
  },
  boxIndicator: {
    bottom: 12,
    left: 16,
  },
  indicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginHorizontal: 2,
  },
  activeIndicator: {
    backgroundColor: '#fff',
  },
  inactiveIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  closeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default AdBanner;
