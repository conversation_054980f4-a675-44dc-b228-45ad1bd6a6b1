import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const CategoryButton = ({ icon, title, isActive, onPress }) => (
  <TouchableOpacity
    style={[styles.categoryButton, isActive && styles.activeCategoryButton]}
    onPress={onPress}
  >
    <Ionicons name={icon} size={24} color={isActive ? "#fff" : "#FF0000"} />
    <Text style={[styles.categoryButtonText, isActive && styles.activeCategoryButtonText]}>{title}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  categoryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    flex: 1,
  },
  activeCategoryButton: {
    backgroundColor: '#FF0000',
    borderRadius: 4,
  },
  categoryButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginTop: 4,
  },
  activeCategoryButtonText: {
    color: '#fff',
  },
});

export default CategoryButton;