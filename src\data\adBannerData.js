// Demo advertisement banner data for testing

export const fullWidthBannerData = [
  {
    id: 'fw1',
    title: 'Premium Truck Finance Available',
    subtitle: 'Get approved in 24 hours',
    backgroundColor: '#bb1010',
    icon: 'card-outline',
    ctaText: 'Apply Now',
    url: 'https://trucksonsale.co.za/finance',
    advertiser: 'TrucksOnSale Finance',
    type: 'finance'
  },
  {
    id: 'fw2',
    title: 'Nationwide Truck Delivery Service',
    subtitle: 'Door-to-door delivery across SA',
    backgroundColor: '#ff8c00',
    icon: 'car-outline',
    ctaText: 'Learn More',
    url: 'https://trucksonsale.co.za/delivery',
    advertiser: 'SA Truck Transport',
    type: 'service'
  },
  {
    id: 'fw3',
    title: 'Comprehensive Truck Insurance',
    subtitle: 'Protect your investment today',
    backgroundColor: '#2e8b57',
    icon: 'shield-checkmark-outline',
    ctaText: 'Get Quote',
    url: 'https://trucksonsale.co.za/insurance',
    advertiser: 'TruckGuard Insurance',
    type: 'insurance'
  },
  {
    id: 'fw4',
    title: 'Professional Truck Inspections',
    subtitle: 'Certified pre-purchase inspections',
    backgroundColor: '#4169e1',
    icon: 'checkmark-circle-outline',
    ctaText: 'Book Now',
    url: 'https://trucksonsale.co.za/inspections',
    advertiser: 'TruckCheck Pro',
    type: 'service'
  }
];

export const boxBannerData = [
  {
    id: 'box1',
    title: 'Sell Your Truck Fast',
    subtitle: 'List your truck today and reach thousands of buyers nationwide',
    backgroundColor: '#bb1010',
    icon: 'pricetag-outline',
    ctaText: 'List Now',
    url: 'https://trucksonsale.co.za/sell',
    advertiser: 'TrucksOnSale',
    type: 'platform'
  },
  {
    id: 'box2',
    title: 'Truck Parts & Accessories',
    subtitle: 'Quality parts for all truck makes and models. Fast shipping available.',
    backgroundColor: '#ff8c00',
    icon: 'construct-outline',
    ctaText: 'Shop Parts',
    url: 'https://trucksonsale.co.za/parts',
    advertiser: 'TruckParts SA',
    type: 'parts'
  },
  {
    id: 'box3',
    title: 'Professional Truck Servicing',
    subtitle: 'Expert maintenance and repairs by certified technicians.',
    backgroundColor: '#2e8b57',
    icon: 'build-outline',
    ctaText: 'Book Service',
    url: 'https://trucksonsale.co.za/service',
    advertiser: 'TruckCare Services',
    type: 'service'
  },
  {
    id: 'box4',
    title: 'Truck Driver Training',
    subtitle: 'Get your heavy vehicle license with our accredited training programs.',
    backgroundColor: '#4169e1',
    icon: 'school-outline',
    ctaText: 'Enroll Today',
    url: 'https://trucksonsale.co.za/training',
    advertiser: 'DriveRight Academy',
    type: 'training'
  },
  {
    id: 'box5',
    title: 'Truck Rental Solutions',
    subtitle: 'Short-term and long-term truck rentals for your business needs.',
    backgroundColor: '#9932cc',
    icon: 'time-outline',
    ctaText: 'Rent Now',
    url: 'https://trucksonsale.co.za/rental',
    advertiser: 'FlexiTruck Rentals',
    type: 'rental'
  }
];

// Alternative banner data with image URLs (for future use when actual images are available)
export const fullWidthBannerDataWithImages = [
  {
    id: 'fwi1',
    title: 'Premium Truck Finance',
    subtitle: 'Low interest rates available',
    imageUrl: 'https://via.placeholder.com/400x90/bb1010/ffffff?text=Finance+Available',
    backgroundColor: 'rgba(187, 16, 16, 0.8)',
    icon: 'card-outline',
    ctaText: 'Apply Now',
    url: 'https://trucksonsale.co.za/finance',
    advertiser: 'TrucksOnSale Finance',
    type: 'finance'
  },
  {
    id: 'fwi2',
    title: 'Nationwide Delivery',
    subtitle: 'Professional truck transport',
    imageUrl: 'https://via.placeholder.com/400x90/ff8c00/ffffff?text=Delivery+Service',
    backgroundColor: 'rgba(255, 140, 0, 0.8)',
    icon: 'car-outline',
    ctaText: 'Learn More',
    url: 'https://trucksonsale.co.za/delivery',
    advertiser: 'SA Truck Transport',
    type: 'service'
  }
];

export const boxBannerDataWithImages = [
  {
    id: 'boxi1',
    title: 'Sell Your Truck',
    subtitle: 'Reach thousands of buyers',
    imageUrl: 'https://via.placeholder.com/300x200/bb1010/ffffff?text=Sell+Your+Truck',
    backgroundColor: 'rgba(187, 16, 16, 0.8)',
    icon: 'pricetag-outline',
    ctaText: 'List Now',
    url: 'https://trucksonsale.co.za/sell',
    advertiser: 'TrucksOnSale',
    type: 'platform'
  },
  {
    id: 'boxi2',
    title: 'Quality Parts',
    subtitle: 'For all truck makes & models',
    imageUrl: 'https://via.placeholder.com/300x200/ff8c00/ffffff?text=Truck+Parts',
    backgroundColor: 'rgba(255, 140, 0, 0.8)',
    icon: 'construct-outline',
    ctaText: 'Shop Now',
    url: 'https://trucksonsale.co.za/parts',
    advertiser: 'TruckParts SA',
    type: 'parts'
  }
];

// Banner analytics tracking (for future implementation)
export const trackBannerImpression = (bannerId, bannerType) => {
  console.log('Banner Impression:', { bannerId, bannerType, timestamp: new Date().toISOString() });
  // Future: Send to analytics service
};

export const trackBannerClick = (bannerId, bannerType, advertiser) => {
  console.log('Banner Click:', { 
    bannerId, 
    bannerType, 
    advertiser, 
    timestamp: new Date().toISOString() 
  });
  // Future: Send to analytics service and handle URL opening
};

// Banner configuration
export const bannerConfig = {
  fullWidth: {
    rotationInterval: 6000, // 6 seconds
    autoPlay: true,
    showIndicators: true,
    showCloseButton: true
  },
  box: {
    rotationInterval: 6000, // 6 seconds
    autoPlay: true,
    showIndicators: true,
    showCloseButton: true
  }
};

// Helper function to get random banners for testing
export const getRandomBanners = (type, count = 3) => {
  const sourceData = type === 'full-width' ? fullWidthBannerData : boxBannerData;
  const shuffled = [...sourceData].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, sourceData.length));
};

// Helper function to filter banners by type
export const getBannersByType = (bannerType, category = null) => {
  const sourceData = bannerType === 'full-width' ? fullWidthBannerData : boxBannerData;
  
  if (!category) {
    return sourceData;
  }
  
  return sourceData.filter(banner => banner.type === category);
};

export default {
  fullWidthBannerData,
  boxBannerData,
  fullWidthBannerDataWithImages,
  boxBannerDataWithImages,
  bannerConfig,
  trackBannerImpression,
  trackBannerClick,
  getRandomBanners,
  getBannersByType
};
