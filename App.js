import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { View, TouchableOpacity, StyleSheet, ImageBackground, Dimensions, Share , Text} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';


import HomeScreen from './src/screens/HomeScreen';
import OnSaleScreen from './src/screens/OnSaleScreen';
import Rent2OwnScreen from './src/screens/Rent2OwnScreen';
import HireScreen from './src/screens/HireScreen';
import AuctionScreen from './src/screens/AuctionScreen';
import TruckDetailsScreen from './src/screens/TruckDetailsScreen';
import HireDetailsScreen from './src/screens/HireDetailsScreen';
import SellTruckScreen from './src/screens/SellTruckScreen';
import SalesTeamScreen from './src/screens/SalesTeamScreen';
import CustomSidebar from './src/components/CustomSidebar';
import AdvancedSearchScreen from './src/screens/AdvancedSearchScreen';
import ChatWithSalesScreen from './src/components/ChatWithSales';
import AdvancedSearchDetailsScreen from './src/screens/AdvancedSearchDetailsScreen';
import FeaturedListingsScreen from './src/screens/FeaturedListingsScreen';
import SimpleFeaturedListingsScreen from './src/screens/SimpleFeaturedListingsScreen';
import SimpleLatestListingsScreen from './src/screens/SimpleLatestListingsScreen';
import LatestListingsScreen from './src/screens/LatestListingsScreen';
import ErrorBoundary from './src/components/ErrorBoundary';
import SafeScreenWrapper from './src/components/SafeScreenWrapper';
import ApiDebugger from './src/components/ApiDebugger';
import DealershipsScreen from './src/screens/DealershipsScreen';
import DealershipDetailsScreen from './src/screens/DealershipDetailsScreen';
import RegisterDealerScreen from './src/screens/RegisterDealerScreen';
import PrivacyPolicyScreen from './src/screens/PrivacyPolicyScreen';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Custom Header Component with splash.jpg background
const CustomHeader = ({ onMenuPress, showTitle = false, title = '', navigation, route }) => {
  // Helper to get IDs/content for sharing
  const getShareData = () => {
    const routeName = route?.name || 'Home';

    // Try to get IDs from route params or navigation state
    if (route && route.params) {
      // Single vehicle detail
      if (route.params.vehicle && route.params.vehicle.vehicle_id) {
        const vehicle = route.params.vehicle;
        return {
          type: 'vehicle',
          ids: [vehicle.vehicle_id],
          title: `${vehicle.year || ''} ${vehicle.make || ''} ${vehicle.model || ''}`.trim(),
          description: `Check out this ${vehicle.category || 'vehicle'} for ${vehicle.listing_type === 'hire' ? 'hire' : 'sale'}`,
          price: vehicle.price || vehicle.daily_rate || null,
          location: vehicle.city || vehicle.region || null,
          routeName
        };
      }
      // Dealership details
      if (route.params.dealership && route.params.dealership.user_id) {
        const dealer = route.params.dealership;
        return {
          type: 'dealership',
          ids: [dealer.user_id],
          title: dealer.company_name || dealer.username,
          description: `View vehicles from ${dealer.company_name || dealer.username}`,
          location: dealer.physical_address || null,
          routeName
        };
      }
      // Multiple vehicles (listings)
      if (route.params.vehicles && Array.isArray(route.params.vehicles)) {
        return {
          type: 'list',
          ids: route.params.vehicles.map(v => v.vehicle_id).filter(Boolean),
          title: 'Vehicle Listings',
          description: `Browse ${route.params.vehicles.length} vehicles`,
          routeName
        };
      }
    }

    // Screen-specific sharing based on route name
    switch (routeName) {
      case 'Home':
        return {
          type: 'home',
          ids: [],
          title: 'Trucks On Sale - Home',
          description: 'Browse thousands of trucks, commercial vehicles and more',
          routeName,
          category: route.params?.selectedCategory || 'trucks',
          showListings: true
        };
      case 'FeaturedListings':
        return {
          type: 'featured',
          ids: [],
          title: 'Featured Vehicles',
          description: 'Check out our featured vehicle listings',
          routeName,
          category: route.params?.category || null,
          showListings: true
        };
      case 'LatestListings':
        return {
          type: 'latest',
          ids: [],
          title: 'Latest Vehicles',
          description: 'Browse the latest vehicle listings',
          routeName,
          category: route.params?.category || null,
          dealerId: route.params?.dealerId || null,
          showListings: true
        };
      case 'HireScreen':
        return {
          type: 'hire',
          ids: [],
          title: 'Vehicle Hire',
          description: 'Find vehicles available for hire',
          routeName,
          category: route.params?.category || null,
          showListings: true
        };
      case 'DealershipsScreen':
        return {
          type: 'dealerships',
          ids: [],
          title: 'Verified Dealerships',
          description: 'Browse our network of verified dealerships',
          routeName,
          showListings: false
        };
      default:
        return {
          type: 'generic',
          ids: [],
          title: 'Trucks On Sale',
          description: 'South Africa\'s premier truck marketplace',
          routeName,
          showListings: false
        };
    }
  };

  const handleShare = async () => {
    try {
      const shareData = getShareData();
      const { type, ids, title, description, price, location, routeName, category, dealerId, showListings } = shareData;

      // Build share URL with parameters
      let shareUrl = 'https://trucks24.co.za/share.php';
      const params = new URLSearchParams();

      params.append('type', type);
      params.append('title', title);
      params.append('description', description);
      params.append('route', routeName);

      if (ids.length > 0) {
        if (type === 'vehicle' || type === 'dealership') {
          params.append('id', ids[0]);
        } else {
          params.append('ids', ids.join(','));
        }
      }

      if (price) {
        params.append('price', price.toString());
      }

      if (location) {
        params.append('location', location);
      }

      // Add screen-specific parameters for fetching current listings
      if (showListings) {
        if (category) {
          params.append('category', category);
        }

        if (dealerId) {
          params.append('dealer_id', dealerId);
        }

        // Set appropriate limit based on screen type
        const listingLimit = type === 'home' ? 12 : 20;
        params.append('limit', listingLimit.toString());

        if (type === 'hire') {
          params.append('listing_type', 'hire');
        }
      }

      shareUrl += '?' + params.toString();

      // Create share message
      let shareMessage = `${title}\n${description}`;
      if (price) {
        shareMessage += `\nPrice: R${price.toLocaleString()}`;
      }
      if (location) {
        shareMessage += `\nLocation: ${location}`;
      }

      // Add context about current listings
      if (showListings) {
        if (type === 'featured') {
          shareMessage += `\n\n🌟 View our featured vehicle listings`;
        } else if (type === 'latest') {
          shareMessage += `\n\n🆕 Browse the latest vehicles`;
        } else if (type === 'hire') {
          shareMessage += `\n\n🚛 Find vehicles available for hire`;
        } else if (type === 'home') {
          shareMessage += `\n\n🏠 See what's currently available`;
        }

        if (category && category !== 'all') {
          shareMessage += ` in ${category}`;
        }
      }

      shareMessage += `\n\nView on Trucks On Sale:\n${shareUrl}`;

      const result = await Share.share({
        message: shareMessage,
        url: shareUrl,
        title: title,
      }, {
        dialogTitle: 'Share from Trucks On Sale',
        subject: title,
      });

      if (result.action === Share.sharedAction) {
        console.log('Shared successfully:', shareData);
      }
    } catch (error) {
      console.error('Share error:', error.message);
    }
  };

  return (
    <View style={styles.headerContainer}>
      <ImageBackground
        source={require('./assets/splash.jpg')}
        style={styles.headerBackground}
        imageStyle={styles.headerImage}
        resizeMode="cover"
      >
        {/* Header content */}
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={onMenuPress}
            
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            accessibilityLabel="Open navigation menu"
            accessibilityRole="button"
          >
            <Ionicons name="menu" size={37} color="#fff" />
          </TouchableOpacity>

          {/* Title */}
          <View style={styles.titleContainer}>
            <Text style={styles.titleHeader}>Trucks</Text>
            <Text style={styles.specialTitle}>On</Text>
            <Text style={styles.titleHeader}>Sale</Text>
          </View>

          {/* Share button */}
          <TouchableOpacity
            onPress={handleShare}
            
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            accessibilityLabel="Share content"
            accessibilityRole="button"
          >
            <Ionicons name="share-social-outline" size={33} color="#fff" />
          </TouchableOpacity>
        </View>
      </ImageBackground>
    </View>
  );
};

// Bottom Tab Navigator Component
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'HomeTab') {
            iconName = focused ? 'car' : 'car-outline';
          } else if (route.name === 'OnSal') {
            iconName = focused ? 'car' : 'car-outline';
          } else if (route.name === 'Rent2Own') {
            iconName = focused ? 'card' : 'card-outline';
          } else if (route.name === 'Hire') {
            iconName = focused ? 'time' : 'time-outline';
          } else if (route.name === 'Auction') {
            iconName = focused ? 'hammer' : 'hammer-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#bb1010',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarLabel: 'On Sale',
        }}
      />
      {/* <Tab.Screen
        name="OnSale"
        component={OnSaleScreen}
        options={{
          tabBarLabel: 'On Sale',
        }}
      /> */}
      <Tab.Screen
        name="Rent2Own"
        component={Rent2OwnScreen}
        options={{
          tabBarLabel: 'Rent2Own',
        }}
      />
      <Tab.Screen
        name="Hire"
        component={HireScreen}
        options={{
          tabBarLabel: 'Hire',
        }}
      />
      <Tab.Screen
        name="Auction"
        component={AuctionScreen}
        options={{
          tabBarLabel: 'Auction',
        }}
      />
    </Tab.Navigator>
  );
}

// Component to handle navigation state and sidebar
function AppNavigator({ currentRoute }) {
  const [sidebarVisible, setSidebarVisible] = useState(false);

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBar style="light" backgroundColor="#000000ff" translucent={false} />
      <Stack.Navigator
        screenOptions={{
          header: ({ route, navigation }) => (
            <CustomHeader
              onMenuPress={toggleSidebar}
              showTitle={false}
              title={route.params?.title || ''}
              navigation={navigation}
              route={route}
            />
          ),
        }}
      >
            <Stack.Screen
              name="Home"
              component={MainTabNavigator}
            />
            <Stack.Screen
              name="HomeScreen"
              component={HomeScreen}
            />
            <Stack.Screen
              name="TruckDetails"
              component={TruckDetailsScreen}
            />
            <Stack.Screen
              name="HireDetails"
              component={HireDetailsScreen}
            />
            <Stack.Screen
              name="SellTruck"
              component={SellTruckScreen}
            />
            <Stack.Screen
              name="SalesTeam"
              component={SalesTeamScreen}
            />
            <Stack.Screen
              name="AdvancedSearch"
              component={AdvancedSearchScreen}
            />
            <Stack.Screen
              name="ChatWithSales"
              component={ChatWithSalesScreen}
            />
            <Stack.Screen
              name="AdvancedSearchDetails"
              component={AdvancedSearchDetailsScreen}
            />
            <Stack.Screen
              name="FeaturedListings"
              component={FeaturedListingsScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LatestListings"
              component={LatestListingsScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ApiDebugger"
              component={ApiDebugger}
            />
            <Stack.Screen
              name="DealershipsScreen"
              component={DealershipsScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="DealershipDetails"
              component={DealershipDetailsScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="RegisterDealerScreen"
              component={RegisterDealerScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PrivacyPolicyScreen"
              component={PrivacyPolicyScreen}
              options={{
                headerShown: false,
              }}
            />
        </Stack.Navigator>

        {sidebarVisible && (
          <CustomSidebar
            onClose={toggleSidebar}
            visible={sidebarVisible}
            currentRoute={currentRoute}
          />
        )}
    </View>
  );
}

export default function App() {
  const [currentRoute, setCurrentRoute] = useState('Home');



  const handleNavigationStateChange = (state) => {
    if (state) {
      const route = state.routes[state.index];
      if (route) {
        setCurrentRoute(route.name);
      }
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <NavigationContainer onStateChange={handleNavigationStateChange}>
          <AppNavigator currentRoute={currentRoute} />
        </NavigationContainer>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  // Custom Header Styles
  headerContainer: {
    height: SCREEN_WIDTH < 375 ? 70 : 80, // Responsive height
    width: '100%',
    justifyContent: 'center',
  },
  headerBackground: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'black'
  },
  headerImage: {
    opacity: 0, // Slightly more transparent for better contrast
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 2, // Responsive padding
    paddingTop: SCREEN_WIDTH < 375 ? 6 : 8,
    height: '100%',
  },
  menuButton: {
    // paddingTop: SCREEN_WIDTH < 375 ? 6 : 8, // Responsive padding
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)', // Slightly more visible
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
    // Add border for better definition
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: 5,
  },

  titleHeader:{
      fontSize: SCREEN_WIDTH < 375 ? 25 : 45,
      fontWeight: 'bold',
      color: '#fff',
      marginHorizontal: 2,
  },

  specialTitle: {
    fontSize: SCREEN_WIDTH < 375 ? 20 : 44,
    fontWeight: 'bold',
    color: '#FF0000',
    marginHorizontal: 2,
  
  },

  rightSpacer: {
    width: SCREEN_WIDTH < 375 ? 32 : 40, // Responsive width for balance
  },
  shareButton: {
    marginRight: 4,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    padding: 8,
  },
});