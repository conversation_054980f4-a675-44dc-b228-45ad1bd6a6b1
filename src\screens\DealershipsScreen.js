import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { getDealerships } from '../utils/api';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const DealershipsScreen = () => {
  const navigation = useNavigation();
  const isMountedRef = useRef(true);
  const [dealerships, setDealerships] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ total: 0, page: 1, limit: 20, pages: 0 });

  const fetchDealerships = useCallback(async (page = 1) => {
    try {
      if (!isMountedRef.current) return;

      setLoading(true);
      setError(null);

      const response = await getDealerships(page, 20);

      if (!isMountedRef.current) return;

      if (response.success) {
        setDealerships(response.dealerships);
        setPagination(response.pagination);
      } else {
        setError(response.error || 'Failed to fetch dealerships');
      }
    } catch (err) {
      console.error('Error fetching dealerships:', err);
      if (isMountedRef.current) {
        setError('Failed to load dealerships. Please try again.');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  useEffect(() => {
    isMountedRef.current = true;
    fetchDealerships();

    return () => {
      isMountedRef.current = false;
    };
  }, [fetchDealerships]);

  // Handle screen focus to ensure component is properly mounted
  useFocusEffect(
    useCallback(() => {
      isMountedRef.current = true;
      return () => {
        // Don't set to false here as it interferes with navigation
      };
    }, [])
  );

  const handleCall = useCallback((phone) => {
    if (!isMountedRef.current || !phone) return;
    try {
      Linking.openURL(`tel:${phone}`);
    } catch (error) {
      console.error('Error opening phone dialer:', error);
    }
  }, []);

  const handleEmail = useCallback((email) => {
    if (!isMountedRef.current || !email) return;
    try {
      Linking.openURL(`mailto:${email}`);
    } catch (error) {
      console.error('Error opening email client:', error);
    }
  }, []);

  const handleViewVehicles = useCallback((dealership) => {
    if (!isMountedRef.current || !dealership) return;

    try {
      // Navigate directly to dealership details page
      navigation.navigate('DealershipDetails', {
        dealership: dealership
      });
    } catch (navError) {
      console.error('Navigation error:', navError);
      Alert.alert('Error', 'Unable to view dealership details. Please try again.');
    }
  }, [navigation]);

  const handleGoBack = useCallback(() => {
    if (!isMountedRef.current) return;
    
    try {
      // Check if we can go back
      if (navigation.canGoBack()) {
        navigation.goBack();
      } else {
        // If we can't go back, navigate to a safe screen
        navigation.navigate('Home'); // Replace with your main screen name
      }
    } catch (navError) {
      console.error('Back navigation error:', navError);
    }
  }, [navigation]);

  const renderStars = useCallback((rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Ionicons key={i} name="star" size={14} color="#FFD700" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Ionicons key="half" name="star-half" size={14} color="#FFD700" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Ionicons key={`empty-${i}`} name="star-outline" size={14} color="#FFD700" />
      );
    }

    return stars;
  }, []);

  const renderDealership = useCallback(({ item }) => {
    // Defensive check to ensure component is still mounted
    if (!isMountedRef.current || !item) return null;

    const dealershipName = item.company_name || item.username;
    const location = item.physical_address || 'Location not specified';
    const vehicleCount = parseInt(item.vehicle_count) || 0;

    return (
      <View style={styles.dealershipCard}>
        <View style={styles.dealershipHeader}>
          <View style={styles.dealershipInfo}>
            <Text style={styles.dealershipName}>{dealershipName}</Text>
            <View style={styles.ratingContainer}>
              <Text style={styles.usernameText}>@{item.username}</Text>
            </View>
          </View>
          <View style={styles.vehicleCountBadge}>
            <Text style={styles.vehicleCountText}>{vehicleCount}</Text>
            <Text style={styles.vehicleCountLabel}>vehicles</Text>
          </View>
        </View>

        <View style={styles.dealershipDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="location" size={16} color="#666" />
            <Text style={styles.detailText}>{location}</Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="time" size={16} color="#666" />
            <Text style={styles.detailText}>
              Joined {new Date(item.registered_at).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="checkmark-circle" size={16} color="#28a745" />
            <Text style={styles.detailText}>Verified Dealer</Text>
          </View>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.callButton]}
            onPress={() => handleCall(item.phone)}
            disabled={!item.phone}
          >
            <Ionicons name="call" size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Call</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.emailButton]}
            onPress={() => handleEmail(item.email)}
            disabled={!item.email}
          >
            <Ionicons name="mail" size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Email</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.viewButton]}
            onPress={() => handleViewVehicles(item)}
          >
            <Ionicons name="storefront" size={16} color="#fff" />
            <Text style={styles.actionButtonText}>View Details</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [handleCall, handleEmail, handleViewVehicles]);

  const keyExtractor = useCallback((item, index) => {
    return item?.user_id ? item.user_id.toString() : `dealer-${index}`;
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#bb1010" />
        <Text style={styles.loadingText}>Loading dealerships...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color="#bb1010" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>All Dealerships</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#bb1010" />
          <Text style={styles.errorTitle}>Unable to Load Dealerships</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchDealerships()}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color="#bb1010" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>All Dealerships</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {pagination.total} verified dealerships
        </Text>
      </View>

      <FlatList
        data={dealerships || []}
        renderItem={renderDealership}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={false}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        getItemLayout={null}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.3,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  statsContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  statsText: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    textAlign: 'center',
  },
  listContainer: {
    padding: 16,
  },
  dealershipCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dealershipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  dealershipInfo: {
    flex: 1,
  },
  dealershipName: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 6,
  },
  ratingText: {
    fontSize: BASE_FONT_SIZE * 0.85,
    color: '#666',
  },
  usernameText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: '#888',
    fontStyle: 'italic',
  },
  vehicleCountBadge: {
    backgroundColor: '#bb1010',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignItems: 'center',
  },
  vehicleCountText: {
    fontSize: BASE_FONT_SIZE * 0.9,
    fontWeight: 'bold',
    color: '#fff',
  },
  vehicleCountLabel: {
    fontSize: BASE_FONT_SIZE * 0.7,
    color: '#fff',
  },
  dealershipDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailText: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  callButton: {
    backgroundColor: '#28a745',
  },
  emailButton: {
    backgroundColor: '#007bff',
  },
  viewButton: {
    backgroundColor: '#bb1010',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE * 0.85,
    fontWeight: '600',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: BASE_FONT_SIZE,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: '#bb1010',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
});

export default DealershipsScreen;