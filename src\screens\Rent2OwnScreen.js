import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  ScrollView,
  Platform,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const Rent2OwnScreen = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Subtle pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.02,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Simplified Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Rent to Own</Text>
          <Text style={styles.headerSubtitle}>Flexible Vehicle Financing</Text>
        </View>
      </View>

      {/* Simple Coming Soon Banner */}
      <Animated.View
        style={[
          styles.bannerContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <View style={styles.bannerContent}>
          {/* Icon */}
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons name="car-key" size={48} color="#FF6B35" />
          </View>

          {/* Content */}
          <Text style={styles.bannerTitle}>Coming Soon!</Text>
          <Text style={styles.bannerSubtitle}>Rent-to-Own Program</Text>
          <Text style={styles.bannerDescription}>
            We're working on bringing you flexible vehicle financing options.
            Check back soon for rent-to-own opportunities!
          </Text>
        </View>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  // Header Styles
  headerContainer: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 24,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#e2e8f0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 2.2,
    fontWeight: "800",
    color: "#1e293b",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#64748b",
    fontWeight: "500",
    letterSpacing: 0.2,
  },
  // Simple Banner Styles
  bannerContainer: {
    marginHorizontal: 16,
    marginVertical: 32,
    backgroundColor: "#FFF8E1",
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#FF8C00",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bannerContent: {
    padding: 24,
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: 16,
  },
  bannerTitle: {
    fontSize: BASE_FONT_SIZE * 1.8,
    fontWeight: "bold",
    color: "#FF8C00",
    marginBottom: 8,
    textAlign: "center",
  },
  bannerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: "600",
    color: "#D2691E",
    marginBottom: 12,
    textAlign: "center",
  },
  bannerDescription: {
    fontSize: BASE_FONT_SIZE * 1.0,
    color: "#8B4513",
    textAlign: "center",
    lineHeight: 20,
    paddingHorizontal: 8,
  },
});

export default Rent2OwnScreen;
