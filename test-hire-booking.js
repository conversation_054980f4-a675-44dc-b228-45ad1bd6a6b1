// Test script for hire booking functionality
const axios = require('axios');

const BASE_URL = 'https://trucks24.co.za/api';

// Test data
const testBooking = {
  vehicle_id: 49, // Assuming vehicle ID 1 exists
  customer_name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+27123456789',
  start_date: '2025-08-01',
  end_date: '2025-08-05',
  pickup_location: 'Cape Town, Western Cape',
  daily_rate: 500,
  total_cost: 2000,
  rental_period: 'daily',
  message: 'Test booking for hire functionality',
  vehicle_details: {
    year: 2020,
    make: 'Mercedes-Benz',
    model: 'Actros',
    category: 'trucks'
  }
};

async function testHireBooking() {
  try {
    console.log('🚛 Testing Hire Booking Functionality');
    console.log('=====================================\n');

    // Test 1: Check vehicle availability
    console.log('1. Testing vehicle availability check...');
    try {
      const availabilityResponse = await axios.get(
        `${BASE_URL}/hire-bookings/vehicle/${testBooking.vehicle_id}/availability`,
        {
          params: {
            start_date: testBooking.start_date,
            end_date: testBooking.end_date
          }
        }
      );
      console.log('✅ Availability check successful:', availabilityResponse.data);
    } catch (error) {
      console.log('❌ Availability check failed:', error.response?.data || error.message);
    }

    // Test 2: Create hire booking
    console.log('\n2. Testing hire booking creation...');
    try {
      const bookingResponse = await axios.post(`${BASE_URL}/hire-bookings`, testBooking);
      console.log('✅ Booking creation successful:', bookingResponse.data);
      
      const bookingId = bookingResponse.data.booking?.id;
      
      if (bookingId) {
        // Test 3: Get booking by ID
        console.log('\n3. Testing booking retrieval...');
        try {
          const getResponse = await axios.get(`${BASE_URL}/hire-bookings/${bookingId}`);
          console.log('✅ Booking retrieval successful:', getResponse.data);
        } catch (error) {
          console.log('❌ Booking retrieval failed:', error.response?.data || error.message);
        }

        // Test 4: Update booking status
        console.log('\n4. Testing booking status update...');
        try {
          const statusResponse = await axios.patch(`${BASE_URL}/hire-bookings/${bookingId}/status`, {
            status: 'confirmed'
          });
          console.log('✅ Status update successful:', statusResponse.data);
        } catch (error) {
          console.log('❌ Status update failed:', error.response?.data || error.message);
        }
      }
    } catch (error) {
      console.log('❌ Booking creation failed:', error.response?.data || error.message);
    }

    // Test 5: Get all bookings
    console.log('\n5. Testing get all bookings...');
    try {
      const allBookingsResponse = await axios.get(`${BASE_URL}/hire-bookings`);
      console.log('✅ Get all bookings successful. Total:', allBookingsResponse.data.total || 0);
    } catch (error) {
      console.log('❌ Get all bookings failed:', error.response?.data || error.message);
    }

    console.log('\n🎉 Hire booking functionality test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testHireBooking();
}

module.exports = { testHireBooking };
