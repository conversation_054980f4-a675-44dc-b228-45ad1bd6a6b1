import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const ListingSkeleton = ({ width = SCREEN_WIDTH * 0.75, height = 200, style }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={[styles.container, { width, height }, style]}>
      {/* Image skeleton */}
      <Animated.View 
        style={[
          styles.imageSkeleton, 
          { opacity: shimmerOpacity }
        ]} 
      />
      
      {/* Content skeleton */}
      <View style={styles.contentSkeleton}>
        {/* Title skeleton */}
        <Animated.View 
          style={[
            styles.titleSkeleton, 
            { opacity: shimmerOpacity }
          ]} 
        />
        
        {/* Subtitle skeleton */}
        <Animated.View 
          style={[
            styles.subtitleSkeleton, 
            { opacity: shimmerOpacity }
          ]} 
        />
        
        {/* Price skeleton */}
        <Animated.View 
          style={[
            styles.priceSkeleton, 
            { opacity: shimmerOpacity }
          ]} 
        />
        
        {/* Badges skeleton */}
        <View style={styles.badgeContainer}>
          <Animated.View 
            style={[
              styles.badgeSkeleton, 
              { opacity: shimmerOpacity }
            ]} 
          />
          <Animated.View 
            style={[
              styles.badgeSkeleton, 
              { opacity: shimmerOpacity }
            ]} 
          />
        </View>
      </View>
    </View>
  );
};

const HorizontalListingSkeleton = ({ count = 3 }) => {
  return (
    <View style={styles.horizontalContainer}>
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} style={styles.horizontalItem}>
          <ListingSkeleton 
            width={SCREEN_WIDTH * 0.75} 
            height={280}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 16,
  },
  imageSkeleton: {
    width: '100%',
    height: '60%',
    backgroundColor: '#e0e0e0',
  },
  contentSkeleton: {
    padding: 12,
    flex: 1,
  },
  titleSkeleton: {
    height: 18,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 8,
    width: '80%',
  },
  subtitleSkeleton: {
    height: 14,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 12,
    width: '60%',
  },
  priceSkeleton: {
    height: 20,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 12,
    width: '50%',
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  badgeSkeleton: {
    height: 24,
    width: 60,
    backgroundColor: '#e0e0e0',
    borderRadius: 12,
  },
  horizontalContainer: {
    flexDirection: 'row',
    paddingHorizontal: 12,
  },
  horizontalItem: {
    marginRight: 12,
  },
});

export { ListingSkeleton, HorizontalListingSkeleton };
export default ListingSkeleton;
