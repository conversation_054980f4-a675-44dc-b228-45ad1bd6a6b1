export const filterByPrice = (itemPrice, priceFilter) => {
  switch(priceFilter) {
    case 'Under R50,000':
      return itemPrice < 50000;
    case 'R50,000 - R100,000':
      return itemPrice >= 50000 && itemPrice <= 100000;
    case 'R100,000 - R200,000':
      return itemPrice >= 100000 && itemPrice <= 200000;
    case 'R200,000 - R500,000':
      return itemPrice >= 200000 && itemPrice <= 500000;
    case 'Over R500,000':
      return itemPrice > 500000;
    default:
      return true;
  }
};