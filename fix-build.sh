#!/bin/bash

echo "🔧 Fixing Expo/React Native build issues..."

# Step 1: Clean everything
echo "📦 Cleaning caches and node_modules..."
rm -rf node_modules
rm -rf .expo
rm -rf android/build
rm -rf android/app/build
npm cache clean --force

# Step 2: Install dependencies
echo "📦 Installing dependencies..."
npm install

# Step 3: Install missing Expo modules with correct versions for SDK 52
echo "📦 Installing missing Expo modules..."
npx expo install expo-modules-core@2.1.5
npx expo install expo-linear-gradient@14.1.1
npx expo install expo-linking@7.1.5

# Step 4: Fix any version mismatches
echo "📦 Fixing version mismatches..."
npx expo install --fix

# Step 5: Prebuild (regenerate native code)
echo "🔨 Regenerating native code..."
npx expo prebuild --clean

# Step 6: Clear Expo cache
echo "🧹 Clearing Expo cache..."
npx expo r -c

echo "✅ Build fix complete! Try building again with:"
echo "   npx expo run:android"
echo "   or"
echo "   eas build --platform android"
