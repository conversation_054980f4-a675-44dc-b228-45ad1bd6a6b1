import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import TruckCard from "../components/TruckCard";
import SafeScreenWrapper from "../components/SafeScreenWrapper";
import { getFeaturedVehicles } from "../utils/api";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

const SimpleFeaturedListingsScreen = () => {
  const navigation = useNavigation();
  const mountedRef = useRef(true);
  
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simple, safe state update function
  const safeSetState = (setter, value) => {
    if (mountedRef.current) {
      try {
        setter(value);
      } catch (err) {
        console.error('State update error:', err);
      }
    }
  };

  // Simplified fetch function
  const fetchVehicles = async () => {
    if (!mountedRef.current) return;

    try {
      safeSetState(setLoading, true);
      safeSetState(setError, null);

      const response = await getFeaturedVehicles(15, false, "trucks");
      
      if (mountedRef.current) {
        safeSetState(setVehicles, response.vehicles || []);
      }
    } catch (err) {
      console.error('Fetch error:', err);
      if (mountedRef.current) {
        safeSetState(setError, "Failed to load featured vehicles");
        safeSetState(setVehicles, []);
      }
    } finally {
      if (mountedRef.current) {
        safeSetState(setLoading, false);
      }
    }
  };

  // Simple navigation handler
  const handleVehiclePress = (vehicle) => {
    if (!mountedRef.current) return;
    
    try {
      navigation.navigate("TruckDetails", { vehicle });
    } catch (err) {
      console.error('Navigation error:', err);
    }
  };

  // Simple back handler
  const handleGoBack = () => {
    if (!mountedRef.current) return;
    
    try {
      navigation.goBack();
    } catch (err) {
      console.error('Back navigation error:', err);
      try {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Home' }],
        });
      } catch (resetErr) {
        console.error('Reset navigation error:', resetErr);
      }
    }
  };

  // Simple effect with cleanup
  useEffect(() => {
    mountedRef.current = true;
    fetchVehicles();

    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Simple render item
  const renderVehicle = ({ item }) => {
    try {
      return (
        <View style={styles.cardContainer}>
          <TruckCard
            item={item}
            onPress={() => handleVehiclePress(item)}
            width="100%"
          />
        </View>
      );
    } catch (err) {
      console.error('Render item error:', err);
      return null;
    }
  };

  // Simple retry handler
  const handleRetry = () => {
    if (mountedRef.current) {
      fetchVehicles();
    }
  };

  return (
    <SafeScreenWrapper navigation={navigation}>
      <SafeAreaView style={styles.container}>
        {/* Simple Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Featured Listings</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Content */}
        <View style={styles.content}>
          {loading ? (
            <View style={styles.centerContainer}>
              <ActivityIndicator size="large" color="#FF0000" />
              <Text style={styles.loadingText}>Loading featured vehicles...</Text>
            </View>
          ) : error ? (
            <View style={styles.centerContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
                <Text style={styles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          ) : vehicles.length === 0 ? (
            <View style={styles.centerContainer}>
              <Ionicons name="star-outline" size={48} color="#999" />
              <Text style={styles.emptyTitle}>No featured vehicles found</Text>
              <Text style={styles.emptySubtitle}>
                Check back later for featured listings
              </Text>
            </View>
          ) : (
            <FlatList
              data={vehicles}
              renderItem={renderVehicle}
              keyExtractor={(item) => `featured-${item.vehicle_id || item.id}`}
              numColumns={2}
              columnWrapperStyle={styles.row}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContainer}
              removeClippedSubviews={true}
              maxToRenderPerBatch={6}
              windowSize={5}
              initialNumToRender={4}
            />
          )}
        </View>
      </SafeAreaView>
    </SafeScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#1a1a1a",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: "#FF0000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  emptyTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  emptySubtitle: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  listContainer: {
    padding: 16,
  },
  row: {
    justifyContent: "space-between",
  },
  cardContainer: {
    width: (SCREEN_WIDTH - 48) / 2,
    marginBottom: 16,
  },
});

export default SimpleFeaturedListingsScreen;
