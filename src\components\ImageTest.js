import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import SafeImage from './SafeImage';

const ImageTest = () => {
  const [testImageUrl, setTestImageUrl] = useState('https://trucksonsale.co.za/uploads/685b322396257__id_73862217_type_main.jpg');

  const testUrls = [
    'https://trucksonsale.co.za/uploads/685b322396257__id_73862217_type_main.jpg',
    'https://www.trucksonsale.co.za/uploads/685b322396257__id_73862217_type_main.jpg',
    'https://via.placeholder.com/400x300?text=Test+Image',
    'https://picsum.photos/400/300',
  ];

  const testImageLoad = (url) => {
    console.log('Testing image URL:', url);
    setTestImageUrl(url);
  };

  const showImageInfo = () => {
    Alert.alert(
      'Current Test Image',
      `URL: ${testImageUrl}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Image Loading Test</Text>
      
      <SafeImage
        source={{ uri: testImageUrl }}
        style={styles.testImage}
        resizeMode="cover"
        fallbackText="Test Image Failed"
      />
      
      <TouchableOpacity style={styles.infoButton} onPress={showImageInfo}>
        <Text style={styles.buttonText}>Show Image Info</Text>
      </TouchableOpacity>
      
      <Text style={styles.subtitle}>Test Different URLs:</Text>
      
      {testUrls.map((url, index) => (
        <TouchableOpacity
          key={index}
          style={[styles.testButton, testImageUrl === url && styles.activeButton]}
          onPress={() => testImageLoad(url)}
        >
          <Text style={[styles.buttonText, testImageUrl === url && styles.activeButtonText]}>
            Test URL {index + 1}
          </Text>
          <Text style={styles.urlText} numberOfLines={1}>
            {url}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 10,
  },
  testImage: {
    width: '100%',
    height: 200,
    marginBottom: 15,
    borderRadius: 8,
  },
  testButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  activeButton: {
    backgroundColor: '#34C759',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  activeButtonText: {
    color: '#fff',
  },
  urlText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.8,
  },
  infoButton: {
    backgroundColor: '#FF9500',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
});

export default ImageTest;
