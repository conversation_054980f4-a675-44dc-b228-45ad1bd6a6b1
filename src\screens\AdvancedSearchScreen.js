import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Clipboard,
  Alert,
  ActivityIndicator,
  Image,
  Dimensions,
  Share,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
  Entypo,
} from "@expo/vector-icons";
import {
  advancedSearchVehicles,
  getMakesByCategory,
  getModelsByMake,
  getTotalListingsCount,
} from "../utils/api";
import ImageCarousel from "../components/ImageCarousel";
import { useNavigation } from "@react-navigation/native";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const categoryOptions = [
  { label: "Trucks", value: "trucks" },
  { label: "Commercial", value: "commercial_vehicles" },
  { label: "Trailers", value: "trailers" },
  { label: "Others", value: "others" },
];

const listingTypeOptions = [
  { label: "All Types", value: "all" },
  { label: "For Sale", value: "sale" },
  { label: "Rent-to-Own", value: "rent-to-own" },
  { label: "For Hire", value: "hire" },
  { label: "Auction", value: "auction" },
];

const conditionOptions = [
  { label: "All Conditions", value: "all" },
  { label: "New", value: "new" },
  { label: "Used", value: "used" },
  { label: "Refurbished", value: "refurbished" },
];

const conditionRatingOptions = [
  { label: "All Ratings", value: "all" },
  { label: "Excellent", value: "excellent" },
  { label: "Very Good", value: "very_good" },
  { label: "Good", value: "good" },
  { label: "Fair", value: "fair" },
  { label: "Poor", value: "poor" },
];

const priceOptions = [
  "Any Price",
  "Under R50,000",
  "R50,000 - R100,000",
  "R100,000 - R200,000",
  "R200,000 - R500,000",
  "R500,000 - R1,000,000",
  "Over R1,000,000",
];

const regionOptions = [
  "All Regions",
  "Gauteng",
  "KwaZulu-Natal",
  "Western Cape",
  "Eastern Cape",
  "Limpopo",
  "Mpumalanga",
  "Free State",
  "North West",
  "Northern Cape",
];

const transmissionOptions = [
  { label: "All Transmissions", value: "all" },
  { label: "Manual", value: "manual" },
  { label: "Automatic", value: "automatic" },
  { label: "Semi-Automatic", value: "semi-automatic" },
];

const fuelTypeOptions = [
  { label: "All Fuel Types", value: "all" },
  { label: "Diesel", value: "diesel" },
  { label: "Petrol", value: "petrol" },
  { label: "Electric", value: "electric" },
  { label: "Hybrid", value: "hybrid" },
  { label: "LPG", value: "lpg" },
];

const engineTypeOptions = [
  { label: "All Engine Types", value: "all" },
  { label: "V6", value: "v6" },
  { label: "V8", value: "v8" },
  { label: "V8 Turbo", value: "v8 turbo" },
  { label: "1ZR", value: "1ZR" },
  { label: "Other", value: "other" },
];

const colorOptions = [
  "All Colors",
  "White",
  "Black",
  "Silver",
  "Grey",
  "Red",
  "Blue",
  "Green",
  "Brown",
  "Yellow",
  "Orange",
  "Other",
];

const AdvancedSearchScreen = () => {
  const navigation = useNavigation();

  // Fetch total listings count on component mount
  useEffect(() => {
    const fetchTotalCount = async () => {
      try {
        console.log('AdvancedSearchScreen: Starting to fetch total listings count...');
        setLoadingTotal(true);

        const count = await getTotalListingsCount();
        console.log('AdvancedSearchScreen: Received total count:', count);

        setTotalListings(count);

        // If count is 0, let's try a quick fallback using existing functions
        if (count === 0) {
          console.log('AdvancedSearchScreen: Count is 0, trying fallback with getLatestVehicles...');
          try {
            // Import the function dynamically to avoid circular imports
            const { getLatestVehicles } = await import('../utils/api');
            const fallbackResult = await getLatestVehicles(50, false, 'all');
            console.log('AdvancedSearchScreen: Fallback result:', fallbackResult);

            if (fallbackResult.vehicles && fallbackResult.vehicles.length > 0) {
              console.log('AdvancedSearchScreen: Using fallback count:', fallbackResult.vehicles.length);
              setTotalListings(fallbackResult.vehicles.length);
            }
          } catch (fallbackError) {
            console.error('AdvancedSearchScreen: Fallback also failed:', fallbackError);
          }
        }

      } catch (error) {
        console.error('AdvancedSearchScreen: Error fetching total listings count:', error);
        setTotalListings(0);
      } finally {
        setLoadingTotal(false);
      }
    };

    fetchTotalCount();
  }, []);

  // Basic filters
  const [selectedCategory, setSelectedCategory] = useState("trucks");
  const [selectedListingType, setSelectedListingType] = useState("all");
  const [selectedCondition, setSelectedCondition] = useState("all");
  const [selectedConditionRating, setSelectedConditionRating] = useState("all");
  const [selectedPrice, setSelectedPrice] = useState("Any Price");
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");

  // Year and mileage filters
  const [minYear, setMinYear] = useState("");
  const [maxYear, setMaxYear] = useState("");
  const [minMileage, setMinMileage] = useState("");
  const [maxMileage, setMaxMileage] = useState("");
  const [minHours, setMinHours] = useState("");
  const [maxHours, setMaxHours] = useState("");

  // Location filters
  const [selectedRegion, setSelectedRegion] = useState("All Regions");
  const [selectedCity, setSelectedCity] = useState("");

  // Vehicle specifications
  const [selectedMakes, setSelectedMakes] = useState([]);
  const [selectedModel, setSelectedModel] = useState("");
  const [selectedVariant, setSelectedVariant] = useState("");
  const [selectedTransmission, setSelectedTransmission] = useState("all");
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [selectedEngineType, setSelectedEngineType] = useState("all");
  const [selectedColor, setSelectedColor] = useState("All Colors");
  const [minHorsepower, setMinHorsepower] = useState("");
  const [maxHorsepower, setMaxHorsepower] = useState("");

  // Feature filters
  const [noAccidents, setNoAccidents] = useState(false);
  const [hasWarranty, setHasWarranty] = useState(false);
  const [financeAvailable, setFinanceAvailable] = useState(false);
  const [tradeInAccepted, setTradeInAccepted] = useState(false);
  const [hasServiceHistory, setHasServiceHistory] = useState(false);
  const [hasRoadworthy, setHasRoadworthy] = useState(false);
  const [featuredOnly, setFeaturedOnly] = useState(false);

  // State management
  const [results, setResults] = useState([]);
  const [shareLink, setShareLink] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [availableMakes, setAvailableMakes] = useState([]);
  const [availableModels, setAvailableModels] = useState([]);
  const [totalListings, setTotalListings] = useState(0);
  const [loadingTotal, setLoadingTotal] = useState(true);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);

  // UI state
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    specifications: false,
    features: false,
    advanced: false,
  });

  // Helper functions
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const resetFilters = () => {
    setSelectedCategory("trucks");
    setSelectedListingType("all");
    setSelectedCondition("all");
    setSelectedConditionRating("all");
    setSelectedPrice("Any Price");
    setMinPrice("");
    setMaxPrice("");
    setMinYear("");
    setMaxYear("");
    setMinMileage("");
    setMaxMileage("");
    setMinHours("");
    setMaxHours("");
    setSelectedRegion("All Regions");
    setSelectedCity("");
    setSelectedMakes([]);
    setSelectedModel("");
    setSelectedVariant("");
    setSelectedTransmission("all");
    setSelectedFuelType("all");
    setSelectedEngineType("all");
    setSelectedColor("All Colors");
    setMinHorsepower("");
    setMaxHorsepower("");
    setNoAccidents(false);
    setHasWarranty(false);
    setFinanceAvailable(false);
    setTradeInAccepted(false);
    setHasServiceHistory(false);
    setHasRoadworthy(false);
    setFeaturedOnly(false);
  };

  // Helper functions for navigation and sharing
  const handleVehiclePress = (vehicle) => {
    if (vehicle.listing_type === 'hire' || vehicle.for_hire) {
      navigation.navigate('HireDetails', { vehicle: vehicle });
    } else {
      navigation.navigate('TruckDetails', { vehicle: vehicle });
    }
  };

  const handleShare = async () => {
    try {
      const searchParams = {
        category: selectedCategory,
        listingType: selectedListingType,
        condition: selectedCondition,
        conditionRating: selectedConditionRating,
        priceRange: selectedPrice,
        minPrice,
        maxPrice,
        minYear,
        maxYear,
        minMileage,
        maxMileage,
        region: selectedRegion,
        makes: selectedMakes,
        model: selectedModel,
        transmission: selectedTransmission,
        fuelType: selectedFuelType,
        engineType: selectedEngineType,
        color: selectedColor,
        features: {
          noAccidents,
          hasWarranty,
          financeAvailable,
          tradeInAccepted,
          hasServiceHistory,
          hasRoadworthy,
          featuredOnly,
        }
      };

      const shareUrl = `https://trucks24.co.za/search?${new URLSearchParams(searchParams).toString()}`;

      await Share.share({
        message: `Check out these vehicles on Trucks On Sale: ${shareUrl}`,
        url: shareUrl,
      });
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert('Error', 'Failed to share search results');
    }
  };

  // Fetch makes when category changes
  useEffect(() => {
    const fetchMakes = async () => {
      try {
        setLoadingMakes(true);
        const makes = await getMakesByCategory(selectedCategory);
        setAvailableMakes(makes);
        // Reset selected makes and models when category changes
        setSelectedMakes([]);
        setSelectedModel("");
        setAvailableModels([]);
      } catch (error) {
        console.error("Error fetching makes:", error);
        // Don't set global error for makes fetching failure
        // setError("Failed to fetch makes. Please try again.");
      } finally {
        setLoadingMakes(false);
      }
    };

    if (selectedCategory) {
      fetchMakes();
    }
  }, [selectedCategory]);

  // Fetch models when selected makes change
  useEffect(() => {
    const fetchModels = async () => {
      if (selectedMakes.length === 0) {
        setAvailableModels([]);
        return;
      }

      try {
        setLoadingModels(true);
        // Fetch models for each selected make
        const modelPromises = selectedMakes.map((make) =>
          getModelsByMake(make, selectedCategory)
        );
        const modelResults = await Promise.all(modelPromises);

        // Combine and deduplicate models from all selected makes
        const allModels = [...new Set(modelResults.flat())];
        setAvailableModels(allModels);
      } catch (error) {
        console.error("Error fetching models:", error);
        // Don't set global error for models fetching failure
        // setError("Failed to fetch models. Please try again.");
      } finally {
        setLoadingModels(false);
      }
    };

    fetchModels();
  }, [selectedMakes, selectedCategory]);

  // Generate shareable link
  useEffect(() => {
    const params = [];

    // Basic filters
    if (selectedCategory)
      params.push(`category=${encodeURIComponent(selectedCategory)}`);
    if (selectedListingType !== "all")
      params.push(`listingType=${encodeURIComponent(selectedListingType)}`);
    if (selectedCondition !== "all")
      params.push(`condition_type=${encodeURIComponent(selectedCondition)}`);
    if (selectedConditionRating !== "all")
      params.push(`conditionRating=${encodeURIComponent(selectedConditionRating)}`);
    if (selectedPrice !== "Any Price")
      params.push(`price=${encodeURIComponent(selectedPrice)}`);

    // Price range
    if (minPrice) params.push(`minPrice=${encodeURIComponent(minPrice)}`);
    if (maxPrice) params.push(`maxPrice=${encodeURIComponent(maxPrice)}`);

    // Year range
    if (minYear) params.push(`minYear=${encodeURIComponent(minYear)}`);
    if (maxYear) params.push(`maxYear=${encodeURIComponent(maxYear)}`);

    // Mileage range
    if (minMileage) params.push(`minMileage=${encodeURIComponent(minMileage)}`);
    if (maxMileage) params.push(`maxMileage=${encodeURIComponent(maxMileage)}`);

    // Hours range
    if (minHours) params.push(`minHours=${encodeURIComponent(minHours)}`);
    if (maxHours) params.push(`maxHours=${encodeURIComponent(maxHours)}`);

    // Location
    if (selectedRegion !== "All Regions")
      params.push(`region=${encodeURIComponent(selectedRegion)}`);
    if (selectedCity)
      params.push(`city=${encodeURIComponent(selectedCity)}`);

    // Vehicle specs
    if (selectedMakes.length > 0)
      params.push(`make=${selectedMakes.map((m) => encodeURIComponent(m)).join(",")}`);
    if (selectedModel)
      params.push(`model=${encodeURIComponent(selectedModel)}`);
    if (selectedVariant)
      params.push(`variant=${encodeURIComponent(selectedVariant)}`);
    if (selectedTransmission !== "all")
      params.push(`transmission=${encodeURIComponent(selectedTransmission)}`);
    if (selectedFuelType !== "all")
      params.push(`fuelType=${encodeURIComponent(selectedFuelType)}`);
    if (selectedEngineType !== "all")
      params.push(`engineType=${encodeURIComponent(selectedEngineType)}`);
    if (selectedColor !== "All Colors")
      params.push(`color=${encodeURIComponent(selectedColor)}`);

    // Horsepower range
    if (minHorsepower) params.push(`minHorsepower=${encodeURIComponent(minHorsepower)}`);
    if (maxHorsepower) params.push(`maxHorsepower=${encodeURIComponent(maxHorsepower)}`);

    // Features
    if (noAccidents) params.push(`noAccidents=true`);
    if (hasWarranty) params.push(`warranty=true`);
    if (financeAvailable) params.push(`finance=true`);
    if (tradeInAccepted) params.push(`tradeIn=true`);
    if (hasServiceHistory) params.push(`serviceHistory=true`);
    if (hasRoadworthy) params.push(`roadworthy=true`);
    if (featuredOnly) params.push(`featured=true`);

    setShareLink(`https://trucks24.co.za/native.php?${params.join("&")}`);
  }, [
    selectedCategory,
    selectedListingType,
    selectedCondition,
    selectedConditionRating,
    selectedPrice,
    minPrice,
    maxPrice,
    minYear,
    maxYear,
    minMileage,
    maxMileage,
    minHours,
    maxHours,
    selectedRegion,
    selectedCity,
    selectedMakes,
    selectedModel,
    selectedVariant,
    selectedTransmission,
    selectedFuelType,
    selectedEngineType,
    selectedColor,
    minHorsepower,
    maxHorsepower,
    noAccidents,
    hasWarranty,
    financeAvailable,
    tradeInAccepted,
    hasServiceHistory,
    hasRoadworthy,
    featuredOnly,
  ]);

  // Client-side filtering function
  const applyClientSideFilters = (vehicles) => {
    console.log('Applying client-side filters to', vehicles.length, 'vehicles');

    let filtered = [...vehicles];

    // Category filter
    if (selectedCategory) {
      if (selectedCategory === "others") {
        filtered = filtered.filter(vehicle =>
          !["trucks", "commercial_vehicles", "trailers"].includes(vehicle.category?.toLowerCase())
        );
      } else {
        filtered = filtered.filter(vehicle =>
          vehicle.category?.toLowerCase() === selectedCategory.toLowerCase()
        );
      }
      console.log(`After category filter (${selectedCategory}): ${filtered.length} vehicles`);
    }

    // Listing type filter
    if (selectedListingType !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.listing_type === selectedListingType
      );
      console.log(`After listing_type filter (${selectedListingType}): ${filtered.length} vehicles`);
    }

    // Condition type filter
    if (selectedCondition !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.condition_type === selectedCondition
      );
      console.log(`After condition_type filter (${selectedCondition}): ${filtered.length} vehicles`);
    }

    // Condition rating filter
    if (selectedConditionRating !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.condition_rating === selectedConditionRating
      );
      console.log(`After condition_rating filter (${selectedConditionRating}): ${filtered.length} vehicles`);
    }

    // Color filter
    if (selectedColor !== "All Colors") {
      filtered = filtered.filter(vehicle =>
        vehicle.color === selectedColor
      );
      console.log(`After color filter (${selectedColor}): ${filtered.length} vehicles`);
    }

    // Engine type filter
    if (selectedEngineType !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.engine_type === selectedEngineType
      );
      console.log(`After engine_type filter (${selectedEngineType}): ${filtered.length} vehicles`);
    }

    // Transmission filter
    if (selectedTransmission !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.transmission === selectedTransmission
      );
      console.log(`After transmission filter (${selectedTransmission}): ${filtered.length} vehicles`);
    }

    // Fuel type filter
    if (selectedFuelType !== "all") {
      filtered = filtered.filter(vehicle =>
        vehicle.fuel_type === selectedFuelType
      );
      console.log(`After fuel_type filter (${selectedFuelType}): ${filtered.length} vehicles`);
    }

    // Price range filters
    if (selectedPrice !== "Any Price") {
      const [min, max] = selectedPrice
        .split(" - ")
        .map((p) => parseInt(p.replace(/[^0-9]/g, "")));
      if (selectedPrice.startsWith("Under")) {
        filtered = filtered.filter(vehicle => parseFloat(vehicle.price) <= min);
      } else if (selectedPrice.startsWith("Over")) {
        filtered = filtered.filter(vehicle => parseFloat(vehicle.price) >= min);
      } else {
        filtered = filtered.filter(vehicle => {
          const price = parseFloat(vehicle.price);
          return price >= min && price <= max;
        });
      }
      console.log(`After price range filter (${selectedPrice}): ${filtered.length} vehicles`);
    }

    // Custom price range
    if (minPrice) {
      filtered = filtered.filter(vehicle => parseFloat(vehicle.price) >= parseInt(minPrice));
      console.log(`After min price filter (${minPrice}): ${filtered.length} vehicles`);
    }
    if (maxPrice) {
      filtered = filtered.filter(vehicle => parseFloat(vehicle.price) <= parseInt(maxPrice));
      console.log(`After max price filter (${maxPrice}): ${filtered.length} vehicles`);
    }

    // Year range filters
    if (minYear) {
      filtered = filtered.filter(vehicle => vehicle.year >= parseInt(minYear));
      console.log(`After min year filter (${minYear}): ${filtered.length} vehicles`);
    }
    if (maxYear) {
      filtered = filtered.filter(vehicle => vehicle.year <= parseInt(maxYear));
      console.log(`After max year filter (${maxYear}): ${filtered.length} vehicles`);
    }

    // Mileage range filters
    if (minMileage) {
      filtered = filtered.filter(vehicle => vehicle.mileage >= parseInt(minMileage));
      console.log(`After min mileage filter (${minMileage}): ${filtered.length} vehicles`);
    }
    if (maxMileage) {
      filtered = filtered.filter(vehicle => vehicle.mileage <= parseInt(maxMileage));
      console.log(`After max mileage filter (${maxMileage}): ${filtered.length} vehicles`);
    }

    // Hours range filters
    if (minHours) {
      filtered = filtered.filter(vehicle => vehicle.hours_used >= parseInt(minHours));
      console.log(`After min hours filter (${minHours}): ${filtered.length} vehicles`);
    }
    if (maxHours) {
      filtered = filtered.filter(vehicle => vehicle.hours_used <= parseInt(maxHours));
      console.log(`After max hours filter (${maxHours}): ${filtered.length} vehicles`);
    }

    // Region filter
    if (selectedRegion !== "All Regions") {
      filtered = filtered.filter(vehicle => vehicle.region === selectedRegion);
      console.log(`After region filter (${selectedRegion}): ${filtered.length} vehicles`);
    }

    // City filter
    if (selectedCity) {
      filtered = filtered.filter(vehicle =>
        vehicle.city?.toLowerCase().includes(selectedCity.toLowerCase())
      );
      console.log(`After city filter (${selectedCity}): ${filtered.length} vehicles`);
    }

    // Make filter
    if (selectedMakes.length > 0) {
      filtered = filtered.filter(vehicle =>
        selectedMakes.includes(vehicle.make)
      );
      console.log(`After make filter (${selectedMakes.join(', ')}): ${filtered.length} vehicles`);
    }

    // Model filter
    if (selectedModel) {
      filtered = filtered.filter(vehicle => vehicle.model === selectedModel);
      console.log(`After model filter (${selectedModel}): ${filtered.length} vehicles`);
    }

    // Variant filter
    if (selectedVariant) {
      filtered = filtered.filter(vehicle => vehicle.variant === selectedVariant);
      console.log(`After variant filter (${selectedVariant}): ${filtered.length} vehicles`);
    }

    // Horsepower range filters
    if (minHorsepower) {
      filtered = filtered.filter(vehicle => vehicle.horsepower >= parseInt(minHorsepower));
      console.log(`After min horsepower filter (${minHorsepower}): ${filtered.length} vehicles`);
    }
    if (maxHorsepower) {
      filtered = filtered.filter(vehicle => vehicle.horsepower <= parseInt(maxHorsepower));
      console.log(`After max horsepower filter (${maxHorsepower}): ${filtered.length} vehicles`);
    }

    // Boolean feature filters
    if (noAccidents) {
      filtered = filtered.filter(vehicle => vehicle.no_accidents === 1);
      console.log(`After no_accidents filter: ${filtered.length} vehicles`);
    }
    if (hasWarranty) {
      filtered = filtered.filter(vehicle => vehicle.warranty === 1);
      console.log(`After warranty filter: ${filtered.length} vehicles`);
    }
    if (financeAvailable) {
      filtered = filtered.filter(vehicle => vehicle.finance_available === 1);
      console.log(`After finance_available filter: ${filtered.length} vehicles`);
    }
    if (tradeInAccepted) {
      filtered = filtered.filter(vehicle => vehicle.trade_in === 1);
      console.log(`After trade_in filter: ${filtered.length} vehicles`);
    }
    if (hasServiceHistory) {
      filtered = filtered.filter(vehicle => vehicle.service_history === 1);
      console.log(`After service_history filter: ${filtered.length} vehicles`);
    }
    if (hasRoadworthy) {
      filtered = filtered.filter(vehicle => vehicle.roadworthy === 1);
      console.log(`After roadworthy filter: ${filtered.length} vehicles`);
    }
    if (featuredOnly) {
      filtered = filtered.filter(vehicle => vehicle.featured === 1);
      console.log(`After featured filter: ${filtered.length} vehicles`);
    }

    console.log(`Final filtered result: ${filtered.length} vehicles`);
    return filtered;
  };

  // Fetch and filter results with client-side filtering
  useEffect(() => {
    console.log('Advanced search useEffect triggered');
    const fetchResults = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch ALL vehicles without backend filters (since backend filtering isn't working)
        // Only use basic parameters that we know work
        const basicFilters = {
          limit: 100, // Get more vehicles to filter on client side
          status: 'available' // Only get available vehicles
        };

        console.log('Fetching vehicles with basic filters:', basicFilters);
        const response = await advancedSearchVehicles(basicFilters);
        let vehicles = response.vehicles || [];
        console.log(`Fetched ${vehicles.length} vehicles from API`);

        // Apply client-side filtering
        const filteredVehicles = applyClientSideFilters(vehicles);

        setResults(filteredVehicles);
      } catch (err) {
        console.error("Advanced search error:", err);
        console.error("Error details:", err.response?.data || err.message);
        setError("Failed to fetch results. Please try again.");
        setResults([]);
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [
    selectedCategory,
    selectedListingType,
    selectedCondition,
    selectedConditionRating,
    selectedPrice,
    minPrice,
    maxPrice,
    minYear,
    maxYear,
    minMileage,
    maxMileage,
    minHours,
    maxHours,
    selectedRegion,
    selectedCity,
    selectedMakes,
    selectedModel,
    selectedVariant,
    selectedTransmission,
    selectedFuelType,
    selectedEngineType,
    selectedColor,
    minHorsepower,
    maxHorsepower,
    noAccidents,
    hasWarranty,
    financeAvailable,
    tradeInAccepted,
    hasServiceHistory,
    hasRoadworthy,
    featuredOnly,
  ]);

  // Render helper components
  const renderSectionHeader = (title, section, icon) => (
    <TouchableOpacity
      style={styles.sectionHeader}
      onPress={() => toggleSection(section)}
      activeOpacity={0.7}
    >
      <View style={styles.sectionHeaderLeft}>
        <Ionicons name={icon} size={20} color="#bb1010" style={styles.sectionIcon} />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      <Ionicons
        name={expandedSections[section] ? "chevron-up" : "chevron-down"}
        size={20}
        color="#666"
      />
    </TouchableOpacity>
  );

  const renderChipSelector = (options, selectedValue, onSelect, isMultiple = false) => (
    <View style={styles.chipContainer}>
      {options.map((option) => {
        const value = typeof option === 'object' ? option.value : option;
        const label = typeof option === 'object' ? option.label : option;
        const isSelected = isMultiple
          ? selectedValue.includes(value)
          : selectedValue === value;

        return (
          <TouchableOpacity
            key={value}
            style={[
              styles.modernChip,
              isSelected && styles.modernChipActive,
            ]}
            onPress={() => onSelect(value)}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.modernChipText,
                isSelected && styles.modernChipTextActive,
              ]}
            >
              {label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  const renderRangeInput = (label, minValue, maxValue, onMinChange, onMaxChange, placeholder = "") => (
    <View style={styles.rangeContainer}>
      <Text style={styles.rangeLabel}>{label}</Text>
      <View style={styles.rangeInputs}>
        <TextInput
          style={styles.rangeInput}
          placeholder={`Min ${placeholder}`}
          value={minValue}
          onChangeText={onMinChange}
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
        <Text style={styles.rangeSeparator}>to</Text>
        <TextInput
          style={styles.rangeInput}
          placeholder={`Max ${placeholder}`}
          value={maxValue}
          onChangeText={onMaxChange}
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
      </View>
    </View>
  );

  const renderToggleSwitch = (label, value, onToggle, icon) => (
    <TouchableOpacity
      style={styles.toggleContainer}
      onPress={() => onToggle(!value)}
      activeOpacity={0.7}
    >
      <View style={styles.toggleLeft}>
        <Ionicons name={icon} size={18} color={value ? "#bb1010" : "#666"} />
        <Text style={[styles.toggleLabel, value && styles.toggleLabelActive]}>
          {label}
        </Text>
      </View>
      <View style={[styles.toggleSwitch, value && styles.toggleSwitchActive]}>
        <View style={[styles.toggleThumb, value && styles.toggleThumbActive]} />
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Modern Header */}
      <View style={styles.modernHeader}>
        <View style={styles.headerContent}>
          <Text style={styles.modernTitle}>Advanced Search</Text>
          <Text style={styles.modernSubtitle}>Find your perfect vehicle with detailed filters</Text>
        </View>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetFilters}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={20} color="#bb1010" />
          <Text style={styles.resetButtonText}>Reset</Text>
        </TouchableOpacity>
      </View>

      {/* Total Listings Counter */}
      {/* <View style={styles.totalListingsSection}>
        <View style={styles.totalListingsContainer}>
          <Ionicons name="stats-chart" size={24} color="#bb1010" />
          <View style={styles.totalListingsContent}>
            <Text style={styles.totalListingsTitle}>Total Listings</Text>
            {loadingTotal ? (
              <ActivityIndicator size="small" color="#bb1010" />
            ) : (
              <Text style={styles.totalListingsCount}>
                {totalListings.toLocaleString()} vehicles available
              </Text>
            )}
          </View>
        </View>
      </View> */}

      {/* Search Filters */}
      <View style={styles.filtersContainer}>

        {/* Basic Filters Section */}
        <View style={styles.filterSection}>
          {renderSectionHeader("Basic Filters", "basic", "filter")}

          {expandedSections.basic && (
            <View style={styles.sectionContent}>
              {/* Category */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Category</Text>
                {renderChipSelector(categoryOptions, selectedCategory, setSelectedCategory)}
              </View>

              {/* Listing Type */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Listing Type</Text>
                {renderChipSelector(listingTypeOptions, selectedListingType, setSelectedListingType)}
              </View>

              {/* Condition */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Condition</Text>
                {renderChipSelector(conditionOptions, selectedCondition, setSelectedCondition)}
              </View>

              {/* Condition Rating */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Condition Rating</Text>
                {renderChipSelector(conditionRatingOptions, selectedConditionRating, setSelectedConditionRating)}
              </View>

              {/* Price Range */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Price Range</Text>
                <View style={styles.chipContainer}>
                  {priceOptions.map((price) => (
                    <TouchableOpacity
                      key={price}
                      style={[
                        styles.modernChip,
                        selectedPrice === price && styles.modernChipActive,
                      ]}
                      onPress={() => setSelectedPrice(price)}
                      activeOpacity={0.7}
                    >
                      <Text
                        style={[
                          styles.modernChipText,
                          selectedPrice === price && styles.modernChipTextActive,
                        ]}
                      >
                        {price}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Custom Price Range */}
                {renderRangeInput("Custom Price Range", minPrice, maxPrice, setMinPrice, setMaxPrice, "R")}
              </View>

              {/* Year Range */}
              <View style={styles.filterGroup}>
                {renderRangeInput("Year Range", minYear, maxYear, setMinYear, setMaxYear, "Year")}
              </View>

              {/* Region */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Region</Text>
                <View style={styles.chipContainer}>
                  {regionOptions.map((region) => (
                    <TouchableOpacity
                      key={region}
                      style={[
                        styles.modernChip,
                        selectedRegion === region && styles.modernChipActive,
                      ]}
                      onPress={() => setSelectedRegion(region)}
                      activeOpacity={0.7}
                    >
                      <Text
                        style={[
                          styles.modernChipText,
                          selectedRegion === region && styles.modernChipTextActive,
                        ]}
                      >
                        {region}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          )}
        </View>

        {/* Vehicle Specifications Section */}
        <View style={styles.filterSection}>
          {renderSectionHeader("Vehicle Specifications", "specifications", "car-sport")}

          {expandedSections.specifications && (
            <View style={styles.sectionContent}>
              {/* Make Selection */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Make</Text>
                {loadingMakes ? (
                  <ActivityIndicator size="small" color="#bb1010" />
                ) : (
                  <View style={styles.chipContainer}>
                    {availableMakes.map((make) => (
                      <TouchableOpacity
                        key={make}
                        style={[
                          styles.modernChip,
                          selectedMakes.includes(make) && styles.modernChipActive,
                        ]}
                        onPress={() => {
                          if (selectedMakes.includes(make)) {
                            setSelectedMakes(selectedMakes.filter(m => m !== make));
                          } else {
                            setSelectedMakes([...selectedMakes, make]);
                          }
                        }}
                        activeOpacity={0.7}
                      >
                        <Text
                          style={[
                            styles.modernChipText,
                            selectedMakes.includes(make) && styles.modernChipTextActive,
                          ]}
                        >
                          {make}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>

              {/* Model Selection */}
              {selectedMakes.length > 0 && (
                <View style={styles.filterGroup}>
                  <Text style={styles.filterLabel}>Model</Text>
                  {loadingModels ? (
                    <ActivityIndicator size="small" color="#bb1010" />
                  ) : (
                    <View style={styles.chipContainer}>
                      {availableModels.map((model) => (
                        <TouchableOpacity
                          key={model}
                          style={[
                            styles.modernChip,
                            selectedModel === model && styles.modernChipActive,
                          ]}
                          onPress={() => setSelectedModel(selectedModel === model ? "" : model)}
                          activeOpacity={0.7}
                        >
                          <Text
                            style={[
                              styles.modernChipText,
                              selectedModel === model && styles.modernChipTextActive,
                            ]}
                          >
                            {model}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>
              )}

              {/* Transmission */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Transmission</Text>
                {renderChipSelector(transmissionOptions, selectedTransmission, setSelectedTransmission)}
              </View>

              {/* Fuel Type */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Fuel Type</Text>
                {renderChipSelector(fuelTypeOptions, selectedFuelType, setSelectedFuelType)}
              </View>

              {/* Engine Type */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Engine Type</Text>
                {renderChipSelector(engineTypeOptions, selectedEngineType, setSelectedEngineType)}
              </View>

              {/* Color */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Color</Text>
                <View style={styles.chipContainer}>
                  {colorOptions.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.modernChip,
                        selectedColor === color && styles.modernChipActive,
                      ]}
                      onPress={() => setSelectedColor(color)}
                      activeOpacity={0.7}
                    >
                      <Text
                        style={[
                          styles.modernChipText,
                          selectedColor === color && styles.modernChipTextActive,
                        ]}
                      >
                        {color}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Mileage Range */}
              <View style={styles.filterGroup}>
                {renderRangeInput("Mileage Range", minMileage, maxMileage, setMinMileage, setMaxMileage, "km")}
              </View>

              {/* Hours Used Range */}
              <View style={styles.filterGroup}>
                {renderRangeInput("Hours Used", minHours, maxHours, setMinHours, setMaxHours, "hours")}
              </View>

              {/* Horsepower Range */}
              <View style={styles.filterGroup}>
                {renderRangeInput("Horsepower", minHorsepower, maxHorsepower, setMinHorsepower, setMaxHorsepower, "HP")}
              </View>
            </View>
          )}
        </View>

        {/* Features Section */}
        <View style={styles.filterSection}>
          {renderSectionHeader("Features & Condition", "features", "checkmark-circle")}

          {expandedSections.features && (
            <View style={styles.sectionContent}>
              <View style={styles.toggleGrid}>
                {renderToggleSwitch("No Accidents", noAccidents, setNoAccidents, "shield-checkmark")}
                {renderToggleSwitch("Warranty Available", hasWarranty, setHasWarranty, "shield")}
                {renderToggleSwitch("Finance Available", financeAvailable, setFinanceAvailable, "card")}
                {renderToggleSwitch("Trade-in Accepted", tradeInAccepted, setTradeInAccepted, "swap-horizontal")}
                {renderToggleSwitch("Service History", hasServiceHistory, setHasServiceHistory, "document-text")}
                {renderToggleSwitch("Roadworthy Certificate", hasRoadworthy, setHasRoadworthy, "car-sport")}
                {renderToggleSwitch("Featured Only", featuredOnly, setFeaturedOnly, "star")}
              </View>
            </View>
          )}
        </View>

        {/* Search Button */}
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => {/* Search logic will be handled by existing useEffect */}}
          activeOpacity={0.8}
        >
          <Ionicons name="search" size={20} color="#fff" />
          <Text style={styles.searchButtonText}>Search Vehicles</Text>
        </TouchableOpacity>

        {/* Results Count */}
        {results.length > 0 && (
          <View style={styles.resultsHeader}>
            <Text style={styles.resultsCount}>
              {results.length} vehicle{results.length !== 1 ? 's' : ''} found
            </Text>
            <TouchableOpacity
              style={styles.shareButton}
              onPress={handleShare}
              activeOpacity={0.7}
            >
              <Ionicons name="share" size={16} color="#bb1010" />
              <Text style={styles.shareButtonText}>Share</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Loading State */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#bb1010" />
          <Text style={styles.loadingText}>Searching vehicles...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={24} color="#e74c3c" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Results */}
      {results.length > 0 && (
        <View style={styles.resultsContainer}>
          {results.map((vehicle, index) => (
            <TouchableOpacity
              key={vehicle.vehicle_id || index}
              style={styles.resultCard}
              onPress={() => handleVehiclePress(vehicle)}
              activeOpacity={0.7}
            >
              {/* Vehicle Image Preview */}
              <View style={styles.resultImageContainer}>
                {vehicle.images && vehicle.images.length > 0 ? (
                  <ImageCarousel
                    images={vehicle.images}
                    width={100}
                    height={80}
                    showIndicators={false}
                    showCounter={false}
                    borderRadius={8}
                    style={styles.resultImage}
                  />
                ) : (
                  <View style={styles.resultImagePlaceholder}>
                    <Ionicons name="image-outline" size={32} color="#ccc" />
                  </View>
                )}
              </View>

              {/* Vehicle Details */}
              <View style={styles.resultContent}>
                <Text style={styles.resultTitle}>
                  {vehicle.year} {vehicle.make} {vehicle.model}
                </Text>
                <Text style={styles.resultPrice}>R{vehicle.price?.toLocaleString()}</Text>
                <Text style={styles.resultDetails}>
                  {vehicle.mileage?.toLocaleString()} km • {vehicle.fuel_type} • {vehicle.region}
                </Text>
                {/* Additional details */}
                <View style={styles.resultBadges}>
                  {vehicle.condition_type && (
                    <View style={[styles.resultBadge,
                      vehicle.condition_type === 'new' ? styles.newBadge :
                      vehicle.condition_type === 'used' ? styles.usedBadge : styles.refurbishedBadge
                    ]}>
                      <Text style={styles.resultBadgeText}>{vehicle.condition_type.toUpperCase()}</Text>
                    </View>
                  )}
                  {(vehicle.listing_type === 'hire' || vehicle.for_hire) && (
                    <View style={[styles.resultBadge, styles.hireBadge]}>
                      <Text style={styles.resultBadgeText}>FOR HIRE</Text>
                    </View>
                  )}
                </View>
              </View>

              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },

  // Modern Header Styles
  modernHeader: {
    backgroundColor: "#fff",
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerContent: {
    flex: 1,
  },
  modernTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#2c3e50",
    marginBottom: 4,
  },
  modernSubtitle: {
    fontSize: 14,
    color: "#7f8c8d",
    lineHeight: 20,
  },
  resetButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#bb1010",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  resetButtonText: {
    color: "#bb1010",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },

  // Total Listings Section Styles
  totalListingsSection: {
    backgroundColor: "#fff",
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  totalListingsContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
  },
  totalListingsContent: {
    marginLeft: 12,
    flex: 1,
  },
  totalListingsTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#2c3e50",
    marginBottom: 4,
  },
  totalListingsCount: {
    fontSize: 16,
    fontWeight: "700",
    color: "#bb1010",
  },

  // Filter Container Styles
  filtersContainer: {
    padding: 16,
  },
  filterSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: "hidden",
  },

  // Section Header Styles
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#f8f9fa",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  sectionHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2c3e50",
  },
  sectionContent: {
    padding: 20,
  },

  // Filter Group Styles
  filterGroup: {
    marginBottom: 20,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#2c3e50",
    marginBottom: 12,
  },

  // Modern Chip Styles
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  modernChip: {
    backgroundColor: "#f8f9fa",
    borderWidth: 1,
    borderColor: "#e9ecef",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  modernChipActive: {
    backgroundColor: "#bb1010",
    borderColor: "#bb1010",
  },
  modernChipText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#495057",
  },
  modernChipTextActive: {
    color: "#fff",
    fontWeight: "600",
  },

  // Range Input Styles
  rangeContainer: {
    marginTop: 8,
  },
  rangeLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#2c3e50",
    marginBottom: 8,
  },
  rangeInputs: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  rangeInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: "#e9ecef",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: "#495057",
    backgroundColor: "#fff",
  },
  rangeSeparator: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6c757d",
  },

  // Toggle Switch Styles
  toggleGrid: {
    gap: 12,
  },
  toggleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  toggleLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  toggleLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#495057",
    marginLeft: 12,
  },
  toggleLabelActive: {
    color: "#bb1010",
    fontWeight: "600",
  },
  toggleSwitch: {
    width: 44,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#e9ecef",
    justifyContent: "center",
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: "#bb1010",
  },
  toggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleThumbActive: {
    alignSelf: "flex-end",
  },

  // Button Styles
  searchButton: {
    backgroundColor: "#bb1010",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 20,
    shadowColor: "#bb1010",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  searchButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },

  // Results Header Styles
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2c3e50",
  },
  shareButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    borderWidth: 1,
    borderColor: "#bb1010",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  shareButtonText: {
    color: "#bb1010",
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },

  // State Styles
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#fff",
    marginHorizontal: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  loadingText: {
    marginLeft: 12,
    fontSize: 14,
    color: "#6c757d",
  },
  errorContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#fff",
    marginHorizontal: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: "#e74c3c",
    textAlign: "center",
    marginTop: 12,
    lineHeight: 20,
  },

  // Results Styles
  resultsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  resultCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2c3e50",
    marginBottom: 4,
  },
  resultPrice: {
    fontSize: 18,
    fontWeight: "700",
    color: "#bb1010",
    marginBottom: 4,
  },
  resultDetails: {
    fontSize: 12,
    color: "#6c757d",
    lineHeight: 16,
    marginBottom: 8,
  },
  resultImageContainer: {
    marginRight: 12,
  },
  resultImage: {
    borderRadius: 8,
  },
  resultImagePlaceholder: {
    width: 100,
    height: 80,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  resultBadges: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6,
  },
  resultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  resultBadgeText: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#fff",
  },
  newBadge: {
    backgroundColor: "#4CAF50",
  },
  usedBadge: {
    backgroundColor: "#2196F3",
  },
  refurbishedBadge: {
    backgroundColor: "#FF9800",
  },
  hireBadge: {
    backgroundColor: "#FF8C00",
  },
});

export default AdvancedSearchScreen;

