import React, { useState } from 'react';
import { Image, View, Text, StyleSheet } from 'react-native';

const SafeImage = ({ source, style, resizeMode = 'cover', fallbackText = 'No Image', ...props }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = (error) => {
    console.log('Image loading error:', error.nativeEvent?.error || 'Unknown error');
    console.log('Failed URL:', source?.uri);
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    console.log('Image loaded successfully:', source?.uri);
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageLoadStart = () => {
    console.log('Image loading started:', source?.uri);
    setImageLoading(true);
    setImageError(false);
  };

  // If there's no source URI or it's empty, show fallback immediately
  if (!source?.uri || source.uri === '') {
    return (
      <View style={[style, styles.fallbackContainer]}>
        <Text style={styles.fallbackText}>{fallbackText}</Text>
      </View>
    );
  }

  // If image failed to load, show fallback
  if (imageError) {
    return (
      <View style={[style, styles.fallbackContainer]}>
        <Text style={styles.fallbackText}>{fallbackText}</Text>
        <Text style={styles.errorText}>Failed to load image</Text>
      </View>
    );
  }

  return (
    <Image
      source={source}
      style={style}
      resizeMode={resizeMode}
      onError={handleImageError}
      onLoad={handleImageLoad}
      onLoadStart={handleImageLoadStart}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  fallbackText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    color: '#999',
    fontSize: 12,
    marginTop: 4,
  },
});

export default SafeImage;
