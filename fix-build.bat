@echo off
echo 🔧 Fixing Expo/React Native build issues...

REM Step 1: Clean everything
echo 📦 Cleaning caches and node_modules...
if exist node_modules rmdir /s /q node_modules
if exist .expo rmdir /s /q .expo
if exist android\build rmdir /s /q android\build
if exist android\app\build rmdir /s /q android\app\build
npm cache clean --force

REM Step 2: Install dependencies
echo 📦 Installing dependencies...
npm install

REM Step 3: Install missing Expo modules with correct versions for SDK 52
echo 📦 Installing missing Expo modules...
npx expo install expo-modules-core@2.1.5
npx expo install expo-linear-gradient@14.1.1
npx expo install expo-linking@7.1.5

REM Step 4: Fix any version mismatches
echo 📦 Fixing version mismatches...
npx expo install --fix

REM Step 5: Prebuild (regenerate native code)
echo 🔨 Regenerating native code...
npx expo prebuild --clean

REM Step 6: Clear Expo cache
echo 🧹 Clearing Expo cache...
npx expo r -c

echo ✅ Build fix complete! Try building again with:
echo    npx expo run:android
echo    or
echo    eas build --platform android
pause
