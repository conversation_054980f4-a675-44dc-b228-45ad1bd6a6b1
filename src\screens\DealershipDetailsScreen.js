import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Linking,
  Alert,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import TruckCard from '../components/TruckCard';
import { getVehiclesByDealer } from '../utils/api';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const DealershipDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const isMountedRef = useRef(true);
  const scrollViewRef = useRef(null);
  
  // Get dealership data from navigation params
  const { dealership } = route.params || {};
  
  // State management
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [vehicleStats, setVehicleStats] = useState({
    total: 0,
    forSale: 0,
    forHire: 0,
    categories: []
  });

  // Fetch dealership vehicles
  const fetchDealershipVehicles = useCallback(async (isRefresh = false) => {
    try {
      if (!isMountedRef.current || !dealership?.user_id) return;
      
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      console.log('Fetching vehicles for dealer:', dealership.user_id);

      // Fetch vehicles using dedicated dealer API
      const response = await getVehiclesByDealer(dealership.user_id, 1, 50);

      if (!isMountedRef.current) return;

      if (response.success && response.vehicles) {
        setVehicles(response.vehicles);

        // Calculate statistics
        const stats = calculateVehicleStats(response.vehicles);
        setVehicleStats(stats);

        console.log(`Found ${response.vehicles.length} vehicles for dealer ${dealership.company_name || dealership.username}`);
      } else {
        setVehicles([]);
        setVehicleStats({ total: 0, forSale: 0, forHire: 0, categories: [] });
      }
    } catch (err) {
      console.error('Error fetching dealership vehicles:', err);
      if (isMountedRef.current) {
        setError('Failed to load dealership vehicles. Please try again.');
        setVehicles([]);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        setRefreshing(false);
      }
    }
  }, [dealership]);

  // Calculate vehicle statistics
  const calculateVehicleStats = useCallback((vehicleList) => {
    const stats = {
      total: vehicleList.length,
      forSale: 0,
      forHire: 0,
      categories: []
    };

    const categorySet = new Set();

    vehicleList.forEach(vehicle => {
      // Count by listing type
      if (vehicle.listing_type === 'hire' || vehicle.for_hire === true || vehicle.for_hire === 1) {
        stats.forHire++;
      } else {
        stats.forSale++;
      }

      // Collect categories
      if (vehicle.category) {
        categorySet.add(vehicle.category);
      }
    });

    stats.categories = Array.from(categorySet);
    return stats;
  }, []);

  // Handle vehicle press
  const handleVehiclePress = useCallback((vehicle) => {
    if (!isMountedRef.current || !vehicle) return;
    
    // Use setTimeout to ensure navigation happens after current render cycle
    setTimeout(() => {
      if (!isMountedRef.current) return;
      
      try {
        // Check if the vehicle is for hire
        const isHireVehicle = vehicle.listing_type === 'hire' ||
                             vehicle.for_hire === true ||
                             vehicle.for_hire === 1;

        const targetScreen = isHireVehicle ? "HireDetails" : "TruckDetails";
        console.log(`Navigating to ${targetScreen} for vehicle:`, vehicle.vehicle_id || vehicle.id);

        navigation.navigate(targetScreen, { vehicle });
      } catch (error) {
        console.error('Navigation error:', error);
        // Fallback navigation
        if (isMountedRef.current) {
          try {
            navigation.navigate('TruckDetails', { vehicle });
          } catch (fallbackError) {
            console.error('Fallback navigation error:', fallbackError);
          }
        }
      }
    }, 0);
  }, [navigation]);

  // Handle contact actions
  const handleCall = useCallback((phone) => {
    if (!isMountedRef.current || !phone) return;
    
    setTimeout(() => {
      if (!isMountedRef.current) return;
      
      try {
        Linking.openURL(`tel:${phone}`);
      } catch (error) {
        console.error('Error opening phone dialer:', error);
        Alert.alert('Error', 'Unable to open phone dialer');
      }
    }, 0);
  }, []);

  const handleEmail = useCallback((email) => {
    if (!isMountedRef.current || !email) return;
    
    setTimeout(() => {
      if (!isMountedRef.current) return;
      
      try {
        Linking.openURL(`mailto:${email}`);
      } catch (error) {
        console.error('Error opening email client:', error);
        Alert.alert('Error', 'Unable to open email client');
      }
    }, 0);
  }, []);

  // Handle back navigation
  const handleGoBack = useCallback(() => {
    if (!isMountedRef.current) return;
    
    setTimeout(() => {
      if (!isMountedRef.current) return;
      
      try {
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          navigation.navigate('Home');
        }
      } catch (navError) {
        console.error('Back navigation error:', navError);
      }
    }, 0);
  }, [navigation]);

  // Component lifecycle
  useEffect(() => {
    isMountedRef.current = true;
    
    if (dealership?.user_id) {
      fetchDealershipVehicles();
    }
    
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchDealershipVehicles, dealership]);

  // Handle screen focus
  useFocusEffect(
    useCallback(() => {
      isMountedRef.current = true;
      
      return () => {
        // Don't set to false here as it interferes with navigation
      };
    }, [])
  );

  // Render vehicle item
  const renderVehicle = useCallback(({ item }) => {
    if (!isMountedRef.current || !item) return null;
    
    return (
      <View style={styles.vehicleCardContainer}>
        <TruckCard
          item={item}
          onPress={() => handleVehiclePress(item)}
          width={SCREEN_WIDTH * 0.9}
          style={styles.vehicleCard}
        />
      </View>
    );
  }, [handleVehiclePress]);

  // Handle refresh
  const onRefresh = useCallback(() => {
    if (!isMountedRef.current) return;
    fetchDealershipVehicles(true);
  }, [fetchDealershipVehicles]);

  // Key extractor for FlatList
  const keyExtractor = useCallback((item, index) => {
    return item.vehicle_id ? item.vehicle_id.toString() : `vehicle-${index}`;
  }, []);

  // Validate dealership data
  if (!dealership) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <Ionicons name="arrow-back" size={24} color="#bb1010" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Dealership Details</Text>
          <View style={styles.placeholder} />
        </View>
        
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorText}>Dealership information not found</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleGoBack}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color="#bb1010" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>
          {dealership.company_name || dealership.username}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView 
        ref={scrollViewRef}
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            colors={['#bb1010']}
            tintColor="#bb1010"
          />
        }
      >
        {/* Dealership Info Card */}
        <View style={styles.dealershipCard}>
          <View style={styles.dealershipHeader}>
            <View style={styles.dealershipInfo}>
              <Text style={styles.dealershipName}>
                {dealership.company_name || dealership.username}
              </Text>
              <Text style={styles.dealershipUsername}>@{dealership.username}</Text>
            </View>
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={20} color="#28a745" />
              <Text style={styles.verifiedText}>Verified</Text>
            </View>
          </View>

          {/* Contact Information */}
          <View style={styles.contactSection}>
            {dealership.physical_address && (
              <View style={styles.contactRow}>
                <Ionicons name="location-outline" size={16} color="#666" />
                <Text style={styles.contactText}>{dealership.physical_address}</Text>
              </View>
            )}
            
            {dealership.phone && (
              <TouchableOpacity 
                style={styles.contactRow} 
                onPress={() => handleCall(dealership.phone)}
              >
                <Ionicons name="call-outline" size={16} color="#bb1010" />
                <Text style={[styles.contactText, styles.contactLink]}>{dealership.phone}</Text>
              </TouchableOpacity>
            )}
            
            {dealership.email && (
              <TouchableOpacity 
                style={styles.contactRow} 
                onPress={() => handleEmail(dealership.email)}
              >
                <Ionicons name="mail-outline" size={16} color="#bb1010" />
                <Text style={[styles.contactText, styles.contactLink]}>{dealership.email}</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Statistics */}
          <View style={styles.statsSection}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{vehicleStats.total}</Text>
              <Text style={styles.statLabel}>Total Vehicles</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{vehicleStats.forSale}</Text>
              <Text style={styles.statLabel}>For Sale</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{vehicleStats.forHire}</Text>
              <Text style={styles.statLabel}>For Hire</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{vehicleStats.categories.length}</Text>
              <Text style={styles.statLabel}>Categories</Text>
            </View>
          </View>
        </View>

        {/* Available Listings Section */}
        <View style={styles.listingsSection}>
          <Text style={styles.sectionTitle}>Available Listings</Text>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#bb1010" />
              <Text style={styles.loadingText}>Loading vehicles...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity style={styles.retryButton} onPress={() => fetchDealershipVehicles()}>
                <Text style={styles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          ) : vehicles.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="car-outline" size={48} color="#999" />
              <Text style={styles.emptyTitle}>No vehicles available</Text>
              <Text style={styles.emptySubtitle}>
                This dealership doesn't have any vehicles listed at the moment.
              </Text>
            </View>
          ) : (
            <FlatList
              data={vehicles}
              renderItem={renderVehicle}
              keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
              nestedScrollEnabled={false}
              contentContainerStyle={styles.vehiclesList}
              removeClippedSubviews={false}
              maxToRenderPerBatch={5}
              windowSize={10}
              initialNumToRender={5}
              getItemLayout={null}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  placeholder: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
  },
  dealershipCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dealershipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  dealershipInfo: {
    flex: 1,
  },
  dealershipName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  dealershipUsername: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    fontSize: 12,
    color: '#28a745',
    marginLeft: 4,
    fontWeight: '600',
  },
  contactSection: {
    marginBottom: 20,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  contactLink: {
    color: '#bb1010',
    textDecorationLine: 'underline',
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#bb1010',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  listingsSection: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  vehiclesList: {
    paddingBottom: 16,
  },
  vehicleCardContainer: {
    marginBottom: 26,
    alignItems: 'center',
  },
  vehicleCard: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
    marginVertical: 12,
    paddingHorizontal: 20,
  },
  retryButton: {
    backgroundColor: '#bb1010',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 12,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 20,
  },
});

export default DealershipDetailsScreen;