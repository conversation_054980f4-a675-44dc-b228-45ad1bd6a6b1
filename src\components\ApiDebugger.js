import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { getLatestVehicles, getFeaturedVehicles } from '../utils/api';

const ApiDebugger = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [loading, setLoading] = useState(false);

  const testCategory = async (category) => {
    setLoading(true);
    try {
      console.log(`Testing category: ${category}`);
      
      const [latestResponse, featuredResponse] = await Promise.all([
        getLatestVehicles(5, false, category),
        getFeaturedVehicles(5, false, category)
      ]);

      const info = {
        category,
        latest: {
          count: latestResponse.vehicles?.length || 0,
          vehicles: latestResponse.vehicles || [],
          error: latestResponse.error
        },
        featured: {
          count: featuredResponse.vehicles?.length || 0,
          vehicles: featuredResponse.vehicles || [],
          error: featuredResponse.error
        }
      };

      setDebugInfo(prev => ({
        ...prev,
        [category]: info
      }));

      console.log(`Category ${category} results:`, info);
      
    } catch (error) {
      console.error(`Error testing category ${category}:`, error);
      setDebugInfo(prev => ({
        ...prev,
        [category]: { error: error.message }
      }));
    }
    setLoading(false);
  };

  const testAllCategories = async () => {
    const categories = ['trucks', 'commercial', 'buses', 'others'];
    for (const category of categories) {
      await testCategory(category);
    }
  };

  const showImageDetails = (vehicle) => {
    const imageInfo = vehicle.images?.map((img, index) => 
      `Image ${index + 1}: ${img.image_path}\nPrimary: ${img.is_primary ? 'Yes' : 'No'}\nOrder: ${img.image_order}`
    ).join('\n\n') || 'No images';

    Alert.alert(
      `Vehicle Images - ${vehicle.make} ${vehicle.model}`,
      imageInfo,
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Debugger</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={testAllCategories}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Testing...' : 'Test All Categories'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {Object.entries(debugInfo).map(([category, info]) => (
          <View key={category} style={styles.categorySection}>
            <Text style={styles.categoryTitle}>{category.toUpperCase()}</Text>
            
            {info.error ? (
              <Text style={styles.errorText}>Error: {info.error}</Text>
            ) : (
              <>
                <Text style={styles.countText}>
                  Latest: {info.latest?.count || 0} vehicles
                </Text>
                <Text style={styles.countText}>
                  Featured: {info.featured?.count || 0} vehicles
                </Text>
                
                {info.latest?.vehicles?.slice(0, 2).map((vehicle, index) => (
                  <TouchableOpacity
                    key={`latest-${index}`}
                    style={styles.vehicleItem}
                    onPress={() => showImageDetails(vehicle)}
                  >
                    <Text style={styles.vehicleText}>
                      {vehicle.year} {vehicle.make} {vehicle.model}
                    </Text>
                    <Text style={styles.imageCountText}>
                      Images: {vehicle.images?.length || 0}
                    </Text>
                    <Text style={styles.categoryText}>
                      DB Category: {vehicle.category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#FF0000',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    flex: 1,
  },
  categorySection: {
    backgroundColor: '#fff',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  countText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  errorText: {
    fontSize: 14,
    color: '#FF0000',
    fontStyle: 'italic',
  },
  vehicleItem: {
    backgroundColor: '#f9f9f9',
    padding: 10,
    marginTop: 10,
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: '#FF0000',
  },
  vehicleText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  imageCountText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  categoryText: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
    fontStyle: 'italic',
  },
});

export default ApiDebugger;
